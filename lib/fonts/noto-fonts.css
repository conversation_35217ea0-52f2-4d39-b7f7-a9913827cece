/* Import Noto Sans fonts for comprehensive language support */
@import url('@fontsource/noto-sans/400.css');
@import url('@fontsource/noto-sans/700.css');

/* Additional Noto fonts for specific languages */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap'); /* Simplified Chinese */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;700&display=swap'); /* Traditional Chinese */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;700&display=swap'); /* Japanese */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@400;700&display=swap'); /* Korean */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap'); /* Arabic */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;700&display=swap'); /* Hindi/Devanagari */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@400;700&display=swap'); /* Thai */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Hebrew:wght@400;700&display=swap'); /* Hebrew */

/* Universal font stack for maximum language support */
.noto-universal {
  font-family: 
    "Noto Sans", 
    "Noto Sans CJK SC", 
    "Noto Sans CJK TC", 
    "Noto Sans CJK JP", 
    "Noto Sans CJK KR", 
    "Noto Sans Arabic", 
    "Noto Sans Devanagari", 
    "Noto Sans Thai", 
    "Noto Sans Hebrew",
    system-ui, 
    -apple-system, 
    BlinkMacSystemFont, 
    "Segoe UI", 
    Roboto, 
    "Helvetica Neue", 
    Arial, 
    sans-serif;
}
