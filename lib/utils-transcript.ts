// ====================================================================
// 语言特定的断句符号常量
const SENTENCE_END_MARKERS = {
	// 英语和西欧语言
	WESTERN: [".", "!", "?", "…", ";", ":"],
	// 中文
	CHINESE: ["。", "！", "？", "；", "…"],
	// 日语
	JAPANESE: ["。", "！", "？", "…"],
	// 韩语（现代韩语主要使用西式标点）
	KOREAN: [".", "!", "?", "…"],
	// 希腊语（使用分号作为问号）
	GREEK: [".", "!", ";", "…"],
	// 阿拉伯语和波斯语
	ARABIC: ["؟", "؍", "؎", "؏", "؞", ".", "!"],
	// 印地语和梵语
	HINDI: ["।", "॥"],
	// 泰语
	THAI: ["฿", "๚", "๛"],
	// 亚美尼亚语
	ARMENIAN: ["։", "՜", "՞", "՟"],
	// 西班牙语（包含倒置标点）
	SPANISH: [".", "!", "?", "…", ";", "¿", "¡"],
	// 其他特殊断句符号
	SPECIAL: ["|", "‖", "‗", "‾", "⁇", "⁈", "⁉", "⁏", "⁐", "⁑"],
};
const SENTENCE_END_MARKERS_ALL = new Set(getAllSentenceEndMarkers());
// 获取所有断句符号的组合函数
function getAllSentenceEndMarkers(): string[] {
	return Object.values(SENTENCE_END_MARKERS).flat();
}
// 根据语言获取特定的断句符号
function getSentenceEndMarkersByLanguage(languages: (keyof typeof SENTENCE_END_MARKERS)[]): string[] {
	return languages.flatMap((lang) => SENTENCE_END_MARKERS[lang] || []);
}

/**
 * 将单词数组组合成句子数组
 * @param words 单词数据数组
 * @returns 句子数据数组
 */
export function combineWordsToSentences(words: WordData[] | undefined | null): SentenceData[] {
	if (!words || words.length === 0) {
		return [];
	}

	const sentences: SentenceData[] = [];
	let currentSentenceWords: WordData[] = [];

	for (let i = 0; i < words.length; i++) {
		const currentWord = words[i];
		currentSentenceWords.push(currentWord);

		// 检查当前单词是否包含断句符号
		const containsEndMarker =
			SENTENCE_END_MARKERS_ALL.has(currentWord.word.trim()) || Array.from(SENTENCE_END_MARKERS_ALL).some((marker) => currentWord.word.includes(marker));

		// 如果包含断句符号，或者是最后一个单词，则结束当前句子
		if (containsEndMarker || i === words.length - 1) {
			if (currentSentenceWords.length > 0) {
				const text = currentSentenceWords
					.map((w) => w.word)
					.join("")
					.trim();

				const start = currentSentenceWords[0].start;
				const end = currentSentenceWords[currentSentenceWords.length - 1].end;

				sentences.push({
					text,
					start,
					end,
				});

				currentSentenceWords = [];
			}
		}
	}

	return sentences;
}

/**
 * 更高级的句子组合函数，支持更多的断句规则
 * @param words 单词数据数组
 * @param options 配置选项
 * @returns 句子数据数组
 */
export function advancedCombineWordsToSentences(
	words: WordData[],
	options: {
		maxWordsPerSentence?: number; // 每句最大单词数
		customEndMarkers?: string[]; // 自定义断句符号
		minSentenceLength?: number; // 最小句子长度（字符数）
	} = {},
): SentenceData[] {
	if (!words || words.length === 0) {
		return [];
	}

	const { maxWordsPerSentence = 50, customEndMarkers = [], minSentenceLength = 1 } = options;

	const allEndMarkers = new Set([...SENTENCE_END_MARKERS_ALL, ...customEndMarkers]);

	const sentences: SentenceData[] = [];
	let currentSentenceWords: WordData[] = [];

	for (let i = 0; i < words.length; i++) {
		const currentWord = words[i];
		currentSentenceWords.push(currentWord);

		// 检查断句条件
		const containsEndMarker = Array.from(allEndMarkers).some((marker) => currentWord.word.includes(marker));

		const reachedMaxWords = currentSentenceWords.length >= maxWordsPerSentence;
		const isLastWord = i === words.length - 1;

		// 满足断句条件时结束句子
		if (containsEndMarker || reachedMaxWords || isLastWord) {
			if (currentSentenceWords.length > 0) {
				const text = currentSentenceWords
					.map((w) => w.word)
					.join("")
					.trim();

				// 检查句子长度是否符合要求
				if (text.length >= minSentenceLength) {
					const start = currentSentenceWords[0].start;
					const end = currentSentenceWords[currentSentenceWords.length - 1].end;

					sentences.push({
						text,
						start,
						end,
					});
				}

				currentSentenceWords = [];
			}
		}
	}

	return sentences;
}
