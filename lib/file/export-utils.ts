"use client";

import { saveAs } from "file-saver";
import jsPDF from "jspdf";
import { Document, Packer, Paragraph, TextRun } from "docx";
import html2canvas from "html2canvas";

/**
 * Format time in seconds to MM:SS or HH:MM:SS format
 */
function formatTime(seconds: number): string {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = Math.floor(seconds % 60);

	if (hours > 0) {
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
	}
	return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
}

/**
 * Export sentences as plain text (.txt)
 */
export function exportAsTxt(sentences: SentenceData[], filename: string = "transcript") {
	const content = sentences.map((sentence) => sentence.text).join(" ");
	const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
	saveAs(blob, `${filename}.txt`);
}

/**
 * Export sentences as Markdown (.md)
 */
export function exportAsMarkdown(sentences: SentenceData[], filename: string = "transcript") {
	const content = sentences.map((sentence) => sentence.text).join(" ");
	const blob = new Blob([content], { type: "text/markdown;charset=utf-8" });
	saveAs(blob, `${filename}.md`);
}

/**
 * Export sentences as SRT subtitle format (.srt)
 */
export function exportAsSrt(sentences: SentenceData[], filename: string = "transcript") {
	const srtContent = sentences
		.map((sentence, index) => {
			const startTime = formatSrtTime(sentence.start);
			const endTime = formatSrtTime(sentence.end);

			return `${index + 1}\n${startTime} --> ${endTime}\n${sentence.text}\n`;
		})
		.join("\n");

	const blob = new Blob([srtContent], { type: "text/plain;charset=utf-8" });
	saveAs(blob, `${filename}.srt`);
}

/**
 * Format time for SRT format (HH:MM:SS,mmm)
 */
function formatSrtTime(seconds: number): string {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = Math.floor(seconds % 60);
	const milliseconds = Math.floor((seconds % 1) * 1000);

	return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")},${milliseconds.toString().padStart(3, "0")}`;
}

/**
 * Export sentences as PDF (.pdf) with Unicode font support
 */
export async function exportAsPdf(sentences: SentenceData[], filename: string = "transcript") {
	// Create a temporary HTML element with proper styling for all languages
	const htmlContent = createPdfHtmlContent(sentences, filename);

	// Create a temporary container
	const container = document.createElement("div");
	container.innerHTML = htmlContent;
	container.style.position = "absolute";
	container.style.left = "-9999px";
	container.style.top = "0";
	container.style.width = "210mm"; // A4 width
	container.style.backgroundColor = "white";
	container.style.fontFamily =
		'"Noto Sans", "Noto Sans CJK SC", "Noto Sans CJK TC", "Noto Sans CJK JP", "Noto Sans CJK KR", "Noto Sans Arabic", "Noto Sans Devanagari", "Noto Sans Thai", system-ui, -apple-system, sans-serif';
	container.style.fontSize = "12px";
	container.style.lineHeight = "1.6";
	container.style.padding = "20px";
	container.style.color = "#000";

	document.body.appendChild(container);

	try {
		// Convert HTML to canvas
		const canvas = await html2canvas(container, {
			scale: 2, // Higher resolution
			useCORS: true,
			allowTaint: true,
			backgroundColor: "#ffffff",
			width: container.offsetWidth,
			height: container.offsetHeight,
		});

		// Create PDF from canvas
		const imgData = canvas.toDataURL("image/png");
		const pdf = new jsPDF({
			orientation: "portrait",
			unit: "mm",
			format: "a4",
		});

		const pdfWidth = pdf.internal.pageSize.getWidth();
		const pdfHeight = pdf.internal.pageSize.getHeight(); // Reduced height to add margins
		const imgWidth = pdfWidth;
		const imgHeight = (canvas.height * pdfWidth) / canvas.width;

		let heightLeft = imgHeight;
		let position = 0;

		// Add first page
		pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
		heightLeft -= pdfHeight;

		// Add additional pages if needed
		while (heightLeft >= 0) {
			position = heightLeft - imgHeight;
			pdf.addPage();
			pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
			heightLeft -= pdfHeight;
		}

		pdf.save(`${filename}.pdf`);
	} finally {
		// Clean up
		document.body.removeChild(container);
	}
}

/**
 * Create HTML content for PDF generation with proper styling
 */
function createPdfHtmlContent(sentences: SentenceData[], title: string): string {
	const content = sentences.map((sentence) => sentence.text).join("\na\nb\nc\nd");

	return `
		<div style="max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;">
			<h1 style="font-size: 18px; font-weight: bold; margin-bottom: 20px; color: #000;">${title}</h1>
			<div style="font-size: 12px; line-height: 1.6; color: #000; white-space: pre-wrap;">${content}</div>
		</div>
	`;
}

/**
 * Export sentences as Word document (.docx)
 */
export async function exportAsDocx(sentences: SentenceData[], filename: string = "Transcript") {
	const content = sentences.map((sentence) => sentence.text).join(" ");

	const doc = new Document({
		sections: [
			{
				properties: {},
				children: [
					new Paragraph({
						children: [
							new TextRun({
								text: filename,
								bold: true,
								size: 32,
							}),
						],
					}),
					new Paragraph({
						children: [
							new TextRun({
								text: "",
							}),
						],
					}),
					new Paragraph({
						children: [
							new TextRun({
								text: content,
								size: 24,
							}),
						],
					}),
				],
			},
		],
	});

	const buffer = (await Packer.toBuffer(doc)) as BlobPart;
	const blob = new Blob([buffer], {
		type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	});
	saveAs(blob, `${filename}.docx`);
}

/**
 * Export sentences with timestamps as detailed SRT
 */
export function exportAsDetailedSrt(sentences: SentenceData[], filename: string = "transcript") {
	const srtContent = sentences
		.map((sentence, index) => {
			const startTime = formatSrtTime(sentence.start);
			const endTime = formatSrtTime(sentence.end);

			return `${index + 1}\n${startTime} --> ${endTime}\n${sentence.text}\n`;
		})
		.join("\n");

	const blob = new Blob([srtContent], { type: "text/plain;charset=utf-8" });
	saveAs(blob, `${filename}.srt`);
}

export type ExportFormat = "txt" | "pdf" | "md" | "srt" | "docx";

/**
 * Main export function that handles all formats
 */
export async function exportTranscript(sentences: SentenceData[], format: ExportFormat, filename: string = "transcript") {
	switch (format) {
		case "txt":
			exportAsTxt(sentences, filename);
			break;
		case "pdf":
			await exportAsPdf(sentences, filename);
			break;
		case "md":
			exportAsMarkdown(sentences, filename);
			break;
		case "srt":
			exportAsSrt(sentences, filename);
			break;
		case "docx":
			await exportAsDocx(sentences, filename);
			break;
		default:
			throw new Error(`Unsupported export format: ${format}`);
	}
}
