import { Inter, Comic_Neue } from "next/font/google";

export const fontSans = Inter({
	subsets: ["latin"],
	variable: "--font-sans",
	fallback: [
		"Noto Sans",
		"Noto Sans CJK SC",
		"Noto Sans CJK TC",
		"Noto Sans CJK JP",
		"Noto Sans CJK KR",
		"Noto Sans Arabic",
		"Noto Sans Devanagari",
		"Noto Sans Thai",
		"Noto Sans Hebrew",
		"system-ui",
		"-apple-system",
		"sans-serif",
	],
});
export const fontHeading = Comic_Neue({
	weight: ["300", "400", "700"],
	subsets: ["latin"],
	variable: "--font-heading",
	fallback: ["Noto Sans", "Noto Sans CJK SC", "Noto Sans CJK TC", "Noto Sans CJK JP", "Noto Sans CJK KR", "system-ui", "-apple-system", "sans-serif"],
});
