import { betterAuth, BetterAuthOptions } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { MembershipID, membershipMapping, MembershipPeriodNone } from "@/@types/membership-type";
import * as schema from "../db/schema.server";
import { endOfMonth } from "date-fns";
import { WEB_URL } from "@/lib/constants";
import { getDB } from "../db/db-client.server";
import { checkCfIpCountry, getCfIpCountry } from "./auth-ipcountry";

export const getAuth = () => {
	return betterAuth({
		baseURL: WEB_URL,
		secret: process.env.AUTH_SECRET || undefined,
		database: drizzleAdapter(getDB(), {
			provider: "sqlite",
			schema: {
				user: schema.userSchema,
				account: schema.accountSchema,
				session: schema.sessionSchema,
				verification: schema.verificationSchema,
			},
		}),
		socialProviders: {
			google: {
				clientId: process.env.AUTH_GOOGLE_ID!,
				clientSecret: process.env.AUTH_GOOGLE_SECRET!,
			},
		},
		// advanced: {
		// 	generateId: false,
		// },
		user: {
			// modelName: "userSchema",
			// fields: {
			// 	id: "uid",
			// },
			additionalFields: {
				membershipId: {
					type: "number",
					required: false,
					defaultValue: 0,
					input: false, // don't allow user to set role
				},
				membershipFormatted: {
					type: "string",
					required: false,
					input: false, // don't allow user to set role
				},
				creditFree: {
					type: "number",
					required: false,
					defaultValue: 0,
					input: false, // don't allow user to set role
				},
				creditFreeEndsAt: {
					type: "date",
					required: false,
					input: false, // don't allow user to set role
				},
				countryCode: {
					type: "string",
					required: false,
					input: false, // don't allow user to set role
				},
			},
		},
		session: {
			expiresIn: 60 * 60 * 24 * 7, // 7 days
			updateAge: 60 * 60 * 24, // 1 day (every 1 day the session expiration is updated)
			cookieCache: {
				enabled: true,
				maxAge: 60 * 60 * 24, // 1 day
			},
		},
		databaseHooks: {
			user: {
				create: {
					before: async (user) => {
						const cfIpCountryCode = await getCfIpCountry();
						const isBanIpCountry = await checkCfIpCountry(cfIpCountryCode);
						const freeMembership = membershipMapping[MembershipID.Free];

						return {
							data: {
								...user,
								membershipId: freeMembership.id,
								membershipFormatted: freeMembership.name,
								creditFree: freeMembership.credits,
								// creditFree: isBanIpCountry ? 0 : freeMembership.credits,
								creditFreeEndsAt: endOfMonth(new Date()),
								subscriptionPeriod: MembershipPeriodNone.value,
								countryCode: cfIpCountryCode,
							},
						};
					},
				},
			},
		},
	} as BetterAuthOptions);
};
