import { headers } from "next/headers";

const banIpCountry = [
	"T1", // Tor network
	"ID", // Indonesia
	"IN", // India
	"PH", // Philippines
	"PK", // Pakistan
	"IQ", // Iraq
	"RU", // Russia
	"IR", // Iran
	"LY", // Libya
];

export async function getCfIpCountry(): Promise<string | null> {
	const headersList = await headers();
	return headersList.get("cf-ipcountry");
}

export async function checkCfIpCountry(cfIpCountryCode?: string | null): Promise<boolean | null> {
	let countryCode = cfIpCountryCode;
	if (!countryCode) {
		countryCode = await getCfIpCountry();
	}
	if (countryCode) {
		return banIpCountry.some((country) => country.toUpperCase() === countryCode.toUpperCase());
	}
	return null;
}
