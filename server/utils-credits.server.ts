import { getDB } from "@/server/db/db-client.server";
import { NewUserCreditsHistory, User, userCreditsHistorySchema, userSchema } from "@/server/db/schema.server";
import { eq, sql } from "drizzle-orm";
import { getUserRealtime } from "@/server/utils-user.server";
import { AuthError, Credits402Error } from "@/@types/error";

type UserCreditSource = "creditFree" | "creditOneTime" | "creditSubscription";
export interface UserCreditConsume {
	creditSource: UserCreditSource;
	credit: number;
}

/**
 * Check and return the token source and whether the user has enough tokens.
 * @param userId
 * @param checkOptions
 * @returns { isValid: boolean; tokenSource: UserTokenSource; }
 */
export async function checkUserCredit(
	userId: string,
	checkOptions?: {
		needCredits?: number;
		existUser?: User;
	},
): Promise<{
	isValid: boolean;
	creditConsumes: UserCreditConsume[];
	isUserNotFound: boolean;
	visibility: boolean;
}> {
	const needCredits = checkOptions?.needCredits ?? 1;
	let user: User | null;
	if (checkOptions?.existUser) {
		user = checkOptions.existUser;
	} else {
		user = await getUserRealtime(userId);
		if (!user) {
			throw new AuthError("Not authorized.");
		}
	}

	const creditConsumes: UserCreditConsume[] = [];

	let remainingCredits = needCredits;

	// Try to use tokens in priority order: OneTime -> Subscription -> Free
	if (user.creditOneTime && user.creditOneTime > 0) {
		const creditsToUse = Math.min(user.creditOneTime, remainingCredits);
		if (creditsToUse > 0) {
			creditConsumes.push({
				creditSource: "creditOneTime",
				credit: creditsToUse,
			});
			remainingCredits -= creditsToUse;
		}
	}
	// if (user.creditSubscription && user.creditSubscription > 0) {
	// 	const creditsToUse = Math.min(user.creditSubscription, remainingCredits);
	// 	if (creditsToUse > 0) {
	// 		creditConsumes.push({
	// 			creditSource: "creditSubscription",
	// 			credit: creditsToUse,
	// 		});
	// 		remainingCredits -= creditsToUse;
	// 	}
	// }
	if (user.creditFree && user.creditFree > 0) {
		const creditsToUse = Math.min(user.creditFree, remainingCredits);
		if (creditsToUse > 0) {
			creditConsumes.push({
				creditSource: "creditFree",
				credit: creditsToUse,
			});
			remainingCredits -= creditsToUse;
		}
	}

	// Check if we have enough credits in total
	if (remainingCredits > 0) throw new Credits402Error("You have not enough credits.");
	return {
		isValid: remainingCredits === 0,
		creditConsumes,
		isUserNotFound: false,
		visibility: creditConsumes.length === 1 && creditConsumes[0].creditSource === "creditFree",
	};

	// check credits for free member for subscription app
	// if (user.membershipId === MembershipID.Free) {
	// 	const creditsToUse = Math.min(user.creditFree, needCredits);
	// 	if (creditsToUse > 0) {
	// 		creditConsumes.push({
	// 			creditSource: "creditFree",
	// 			credit: creditsToUse,
	// 		});
	// 		remainingCredits -= creditsToUse;
	// 	}
	// } else {
	// 	// check credits for subscription member
	// 	const creditsToUse = Math.min(user.creditSubscription, needCredits);
	// 	if (creditsToUse > 0) {
	// 		creditConsumes.push({
	// 			creditSource: "creditSubscription",
	// 			credit: creditsToUse,
	// 		});
	// 		remainingCredits -= creditsToUse;
	// 	}
	// }
}

export async function updateUserCredit(
	userId: string,
	creditConsumes: UserCreditConsume[],
	checkOptions?: {
		existUser?: User;
		remark?: string;
	},
): Promise<void> {
	let user: User | null;
	if (checkOptions?.existUser) {
		user = checkOptions.existUser;
	} else {
		user = await getUserRealtime(userId);
	}
	if (!user) return;

	// Create the update object based on tokenConsumes
	const updateObj: any = {
		updatedAt: new Date(),
	};
	const insertUserCreditsHistoryData: NewUserCreditsHistory = {
		userId: user.id,
		remark: checkOptions?.remark,
	};

	// Add token deductions for each token source
	creditConsumes.forEach((consume) => {
		switch (consume.creditSource) {
			case "creditFree":
				if (user?.creditFree) {
					updateObj.creditFree = sql`${userSchema.creditFree} - ${consume.credit}`;
					insertUserCreditsHistoryData.creditsFree = consume.credit;
				}
				break;
			case "creditOneTime":
				if (user?.creditOneTime) {
					updateObj.creditOneTime = sql`${userSchema.creditOneTime} - ${consume.credit}`;
					insertUserCreditsHistoryData.creditsOneTime = consume.credit;
				}
				break;
			case "creditSubscription":
				if (user?.creditSubscription) {
					updateObj.creditSubscription = sql`${userSchema.creditSubscription} - ${consume.credit}`;
					insertUserCreditsHistoryData.creditsSubscription = consume.credit;
				}
				break;
		}
	});

	// Perform the update
	const db = getDB();
	await db.transaction(async (tx) => {
		await tx.update(userSchema).set(updateObj).where(eq(userSchema.id, userId));
		await tx.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);
	});
}
