import { MIXPANEL_TOKEN } from "@/lib/constants";

async function mixpanelTrackRequest(eventName: string, distinctId: string, options?: any) {
	const trackOptions = {
		method: "POST",
		headers: { accept: "text/plain", "content-type": "application/json" },
		body: JSON.stringify([
			{
				event: eventName,
				properties: { token: MIXPANEL_TOKEN, distinct_id: distinctId, ...options },
			},
		]),
	};

	// console.log("trackOptions: ", trackOptions)

	await fetch("https://api.mixpanel.com/track", trackOptions);
}

export function mixpanelTrackEvent(eventName: string, distinctId: string, options?: any) {
	if (process.env.NODE_ENV === "production") {
		void mixpanelTrackRequest(eventName, distinctId);
	}
}
