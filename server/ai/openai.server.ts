import { experimental_transcribe as transcribe } from "ai";
import { openai, createOpenA<PERSON> } from "@ai-sdk/openai";

/**
 * Transcribe audio from a URL using OpenAI Whisper via AI SDK
 * @param fileUrl - URL of the audio file to transcribe
 * @param languageCode - Language code for transcription (e.g., 'en', 'zh', 'es')
 * @returns Promise<string> - The transcribed text
 */
export async function transcribeFromDeepinfra(
	fileUrl: string, // eg. https://example.com/file.mp3
	languageCode: string,
): Promise<string> {
	const provider = createOpenAI({
		baseURL: "https://api.deepinfra.com/v1/openai",
		apiKey: process.env.DEEPINFRA_API_KEY,
	});

	try {
		// Fetch audio data from URL
		// const response = await fetch(fileUrl);
		// if (!response.ok) {
		// 	throw new Error(`Failed to fetch audio file: ${response.status} ${response.statusText}`);
		// }

		// // Get audio data as <PERSON><PERSON><PERSON><PERSON>uffer
		// const audioBuffer = await response.arrayBuffer();
		// const audioData = new Uint8Array(audioBuffer);

		// const result = await transcribe({
		// 	model: provider.transcription("openai/whisper-large-v3-turbo"),
		// 	audio: audioData,
		// 	providerOptions: {
		// 		openai: {
		// 			language: languageCode,
		// 			// Enable word-level timestamps if needed
		// 			// timestampGranularities: ['word'],
		// 		},
		// 	},
		// });
		const result = {
			text: "If you're wondering why this is cut off, it's because the only thing I could stand my phone on was like this, it's kind of the indented suitcase, so it's kind of, this is just there for reasons. I want to let you know I haven't been uploading videos for about a week now. Originally, that was because I was out of town working on movie stuff, TM, but I found out earlier this week, just a few days ago, that my uncle passed away. So I was not prepared for that. Obviously, no one is prepared for that, but I was literally not prepared because I wasn't.  home i wasn't home you know uh so i had to take care of some things first and now i'm going to be going uh to cincinnati to help make arrangements and help you know the rest of my family with that um and i know i've gone for extended periods of time when i was working on movie stuff that i just basically haven't said anything because i was so hyper focused but this is one of those times where i really wanted to let you guys know what was going on um you know i know there's the plan and stuff like that but none of that really matters when there's something  important to take care of. So I'm going to take care of that. And I'm not exactly sure when I'm going to be back. Um, it just depends on how much time I need to be out there. And obviously I know a lot of people are going to say it, but obviously I'm not going to focus on trying to get back to making content or working on other stuff. So these things take precedent and these things need to be, you know, taken care of and, you know, family's so important. So I would never want anyone to.  you know, prioritize work over that. So I'm definitely not going to do the same either. And, and I wouldn't in any other circumstance. Um, so just wanted to let you guys know that way you don't think that I just disappeared working on movie stuff or whatever other inning project that I was, you know, caught off guard with and started to obsess over. So, um, yeah, just wanted to update you on that. Don't worry about anything else. Uh, you know, it'll be a while before I get back. I'm going to take exactly as long as I need.  Um, and it could be, you know, it could be next week. It could be another week. It could be a couple of weeks. Um, but you know, I'm just going to be out there as long as I need to be, uh, to help out. And you know, I'll come back when the time's right. Just want to let you know. All right. So thanks. Love you. Bye.",
		};
		console.log("result:", JSON.stringify(result, null, 2));

		// Return the transcribed text
		return result.text || "";
	} catch (error: any) {
		console.error("Transcription error:", error);

		if (error.message?.includes("fetch")) {
			throw new Error(`Failed to download audio file from URL: ${error.message}`);
		}

		if (error.message?.includes("transcribe")) {
			throw new Error(`Transcription failed: ${error.message}`);
		}

		// Generic error
		throw new Error(`Transcription error: ${error.message || "Unknown error"}`);
	}
}
