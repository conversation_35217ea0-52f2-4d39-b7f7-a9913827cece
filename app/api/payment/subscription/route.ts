import { NextResponse } from "next/server";
import { getSessionUserId } from "@/server/auth/auth-session";
import { WEBNAME } from "@/lib/constants";
import { Polar } from "@polar-sh/sdk";
import { resetUserSubscriptionResume } from "@/server/utils-user.server";
import { SubscriptionCancel } from "@polar-sh/sdk/models/components/subscriptioncancel";
import { SubscriptionUpdateProduct } from "@polar-sh/sdk/models/components/subscriptionupdateproduct";
import { handleApiError } from "@/@types/error-api";

interface Params {
	subscriptionId?: string; // For subscripiton
	productId?: string;
	type: "resume" | "change";
}

export async function POST(req: Request) {
	const params: Params = await req.json();
	const cfIpCountryCode = req.headers.get("cf-ipcountry");

	try {
		const userId = await getSessionUserId();

		const polar = new Polar({
			accessToken: process.env.POLAR_ACCESS_TOKEN ?? "",
			server: process.env.NODE_ENV === "production" ? "production" : "sandbox",
		});

		if (params.type === "resume") {
			// Suspend usage due to API error.
			if (!params.subscriptionId) {
				return NextResponse.json({ status: 400, message: "subscriptionId is required." });
			}
			//TODO: this request got error: Use either `revoke` for immediate cancellation and revokation or `cancel_at_period_end
			const result = await polar.subscriptions.update({
				id: params.subscriptionId,
				subscriptionUpdate: {
					cancelAtPeriodEnd: false,
				} as SubscriptionCancel,
			});
			console.log("result:", result);
			await resetUserSubscriptionResume(userId);
			return NextResponse.json({ status: 200, message: "success" });
		}
		if (params.type === "change") {
			if (!params.subscriptionId) {
				return NextResponse.json({ status: 400, message: "subscriptionId is required." });
			}
			if (!params.productId) {
				return NextResponse.json({ status: 400, message: "productId is required." });
			}
			const result = await polar.subscriptions.update({
				id: params.subscriptionId,
				subscriptionUpdate: {
					productId: params.productId,
					prorationBehavior: "invoice",
				} as SubscriptionUpdateProduct,
			});
			console.log("result:", result);
			return NextResponse.json({ status: 200, message: "success" });
		}

		return NextResponse.json({ status: 500 });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/payment/subscription`);
	}
}
