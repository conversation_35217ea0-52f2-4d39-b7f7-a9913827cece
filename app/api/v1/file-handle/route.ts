import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { saveFileToR2 } from "@/server/r2.server";
import { processMediaFile, readFileInChunks } from "@/lib/file/media-processor";
import { getUUIDString } from "@/lib/utils";
import { getCurrentMonthAndDayString } from "@/lib/utils-date";
import { OSS_URL } from "@/lib/constants";

export async function POST(req: Request) {
	try {
		// Check authentication
		const sessionUser = await getCurrentSessionUser();
		if (!sessionUser) {
			return NextResponse.json({ status: 401, message: "Not authorized." });
		}

		// Parse form data
		const formData = await req.formData();
		const file = formData.get("file") as File;
		const free = formData.get("free") === "true";

		if (!file) {
			return NextResponse.json({ status: 400, message: "No file provided." });
		}

		// Read file in chunks to handle large files efficiently
		const fileBuffer = await readFileInChunks(file);

		// Process the media file (convert video to audio if needed)
		const { buffer: processedBuffer, filename: processedFilename, contentType } = await processMediaFile(fileBuffer, file.name, file.type);

		// Generate file path for R2
		const fileId = getUUIDString();
		const { yearMonth, yearMonthDay } = getCurrentMonthAndDayString();
		const extension = processedFilename.split(".").pop();
		const finalFilename = `${fileId}.${extension}`;

		let urlSuffix = `source${free ? "-free" : ""}/${yearMonth}/${yearMonthDay}-${finalFilename}`;
		if (process.env.NODE_ENV === "development") {
			urlSuffix = `dev/source${free ? "-free" : ""}/${yearMonth}/${yearMonthDay}-${finalFilename}`;
		}
		console.log("urlSuffix:", urlSuffix);

		// Create blob from processed buffer
		const blob = new Blob([new Uint8Array(processedBuffer)], { type: contentType });

		// Upload to R2
		const uploadedPath = await saveFileToR2(urlSuffix, blob, contentType, finalFilename);

		if (!uploadedPath) {
			return NextResponse.json({
				status: 500,
				message: "Failed to upload file to storage.",
			});
		}

		// Generate public URL
		const fileUrl = `${OSS_URL}/${uploadedPath}`;

		return NextResponse.json({
			status: 200,
			fileUrl,
			originalFilename: file.name,
			processedFilename,
			fileSize: processedBuffer.length,
			contentType,
		});
	} catch (error: any) {
		console.error("File upload error:", error);
		return NextResponse.json({
			status: 500,
			message: error.message || "Failed to upload file.",
		});
	}
}
