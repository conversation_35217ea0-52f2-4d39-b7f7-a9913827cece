import React from "react";
import {
	MediaController,
	MediaControlBar,
	MediaTimeRange,
	MediaPlayButton,
	MediaDurationDisplay,
	MediaPlaybackRateButton,
	// MediaTimeDisplay,
	// MediaSeekBackwardButton,
	// MediaSeekForwardButton,
	// MediaMuteButton,
	// MediaVolumeRange,
} from "media-chrome/react";
import { useMediaRef } from "media-chrome/react/media-store";

interface MediaPlayerProps {
	fileUrl: string;
}

export default function MediaPlayer({ fileUrl }: MediaPlayerProps) {
	const mediaRef = useMediaRef();

	const mediaUrl = fileUrl;

	return (
		<div className="mx-auto my-0.5 flex max-w-3xl flex-1 flex-col">
			<MediaController className="" audio>
				<audio ref={mediaRef} slot="media" src={mediaUrl} preload="auto" crossOrigin="" className="" />
				<MediaControlBar className="flex w-full flex-row items-center gap-2 bg-white">
					<MediaPlayButton
						className="h-[36px] w-[36px] shrink-0 rounded-full shadow-md"
						noTooltip
						style={
							{
								"--media-primary-color": "#3b82f6",
								"--media-control-background": "white",
								"--media-control-hover-background": "white",
								"--media-control-padding": "6px",
								"--media-control-height": "20px",
							} as React.CSSProperties
						}
					></MediaPlayButton>
					<MediaTimeRange
						className="h-2 w-full bg-white px-0 text-black"
						style={
							{
								"--media-range-bar-color": "#3b82f6",
								"--media-time-range-buffered-color": "#d4d4d8",
								"--media-range-track-height": "5px",
								"--media-range-track-border-radius": "9999px",
								"--media-range-track-background": "#e5e5e5",
								"--media-range-thumb-background": "white",
								"--media-range-thumb-height": "11px",
								"--media-range-thumb-width": "11px",
								"--media-range-thumb-border": "1px solid #e4e4e7",
							} as React.CSSProperties
						}
					></MediaTimeRange>
					<MediaDurationDisplay
						className="bg-white py-0 font-normal"
						style={
							{
								"--media-primary-color": "#7e858c",
							} as React.CSSProperties
						}
					></MediaDurationDisplay>
					{/* <MediaMuteButton
						className="shrink-0 bg-white px-0"
						noTooltip
						style={
							{
								"--media-control-height": "20px",
								"--media-primary-color": "#3b82f6",
							} as React.CSSProperties
						}
					></MediaMuteButton> */}
					<MediaPlaybackRateButton
						className="hover:bg-muted w-6 rounded-full border bg-white font-sans text-[13px] font-normal text-zinc-700"
						noTooltip
					></MediaPlaybackRateButton>
					{/* <MediaVolumeRange
						className="bg-white"
						style={
							{
								"--media-primary-color": "black",
								"--media-range-track-height": "5px",
								"--media-range-track-border-radius": "9999px",
								"--media-range-track-background": "#e5e5e5",
								"--media-range-thumb-background": "white",
								"--media-range-thumb-height": "11px",
								"--media-range-thumb-width": "11px",
								"--media-range-thumb-border": "1px solid #c8c8c8",
							} as React.CSSProperties
						}
					></MediaVolumeRange> */}
					{/* <MediaControlBar className="flex w-full flex-col bg-white">
						<MediaTimeRange
							className="h-2 w-full bg-white px-2 text-black"
							style={
								{
									"--media-range-bar-color": "black",
									"--media-range-track-background": "#e5e5e5",
									"--media-time-range-buffered-color": "#c8c8c8",
									"--media-range-track-height": "5px",
									"--media-range-track-border-radius": "2px",
									"--media-range-thumb-background": "black",
									"--media-range-thumb-height": "11px",
									"--media-range-thumb-width": "11px",
								} as React.CSSProperties
							}
						></MediaTimeRange>
						<div className="flex w-full flex-row items-center justify-between gap-1 px-1">
							<MediaTimeDisplay
								noToggle
								className="bg-white py-0 font-normal"
								style={
									{
										"--media-primary-color": "#7e858c",
									} as React.CSSProperties
								}
							></MediaTimeDisplay>
							<MediaDurationDisplay
								className="bg-white py-0 font-normal"
								style={
									{
										"--media-primary-color": "#7e858c",
									} as React.CSSProperties
								}
							></MediaDurationDisplay>
						</div>
						<div className="-mt-2 flex flex-row items-center justify-center gap-1">
							<MediaPlaybackRateButton className="bg-white text-sm font-normal text-black"></MediaPlaybackRateButton>
							<MediaSeekBackwardButton
								seekOffset={10}
								className="bg-white text-black"
								noTooltip
								style={
									{
										"--media-primary-color": "black",
									} as React.CSSProperties
								}
							></MediaSeekBackwardButton>
							<MediaPlayButton className="h-8 w-12 rounded-full bg-black text-xs font-normal" noTooltip></MediaPlayButton>
							<MediaSeekForwardButton
								seekOffset={10}
								className="bg-white text-black"
								noTooltip
								style={
									{
										"--media-primary-color": "black",
									} as React.CSSProperties
								}
							></MediaSeekForwardButton>
							<MediaMuteButton
								className="bg-white"
								noTooltip
								style={
									{
										"--media-primary-color": "black",
									} as React.CSSProperties
								}
							></MediaMuteButton>
						</div>
						<MediaTextDisplay>{formatTime(mediaCurrentTime)}</MediaTextDisplay>
					</MediaControlBar> */}
				</MediaControlBar>
			</MediaController>{" "}
		</div>
	);
}
