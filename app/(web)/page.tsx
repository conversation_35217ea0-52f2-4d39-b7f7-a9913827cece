import { WEBNAME } from "@/lib/constants";
import { Metadata } from "next";
import TranscribeClient from "./transcribe.client";

export const metadata: Metadata = {
	title: `${WEBNAME} - Transcribe Audio and Video to Accurate Text`,
	description: "Convert your audio and video to accurate text in seconds with advanced speaker recognition",
	alternates: {
		canonical: "/",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<section>
				<div className="relative pt-12 pb-16">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-5xl text-5xl font-semibold lg:mt-16">Unlimited Audio & Video Transcription</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								<p>Convert your audio and video to accurate text in seconds .</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container w-full px-4">
				<TranscribeClient />
			</div>

			{/* <FAQsComponent
				faqs={[
					{
						question: "",
						answer: "",
					},
				]}
			/> */}

			{/* <FinalCTA
				ctaText=""
				ctaUrl="/"
				title=""
				description=""
			/> */}
		</main>
	);
}
