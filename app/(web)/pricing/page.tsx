import { Metadata } from "next";
import { WEBNAME } from "@/lib/constants";
import Plans from "@/components/shared/plans-subscription";

export const metadata: Metadata = {
	title: `Plans & Pricing | ${WEBNAME}`,
	description: `Explore our flexible and affordable pricing plans for ${WEBNAME}`,
	alternates: {
		canonical: "/pricing",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-16">
			<div className="relative pt-12 pb-12">
				<div className="mx-auto max-w-5xl px-6">
					<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
						<h1 className="mt-8 text-4xl font-medium md:text-5xl">Plans & Pricing</h1>
						{/* <div className="mx-auto mt-4 text-balance text-base md:text-lg">
							New user get {membershipMapping[MembershipID.Free].credits} credits for free.
						</div> */}
					</div>
				</div>
			</div>
			<div className="container flex justify-center">
				<div className="w-full">
					<Plans />
				</div>
			</div>
		</main>
	);
}
