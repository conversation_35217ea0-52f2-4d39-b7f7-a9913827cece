import { Fragment } from "react";
import { Metadata } from "next";
import { WEBNAME } from "@/lib/constants";
import { format } from "date-fns";
import { mentionsAll, newsAll } from "@/config/news";
import { Separator } from "@/components/ui/separator";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import Link from "next/link";

export const metadata: Metadata = {
	title: `${WEBNAME} in the news`,
	description: `Read the latest news about ${WEBNAME}.`,
	alternates: {
		canonical: "/news",
	},
};

export default async function Page() {
	return (
		<div className="flex w-full grow flex-col">
			<div className="bg-zinc-50 pt-12 pb-20">
				<div className="container max-w-3xl">
					<div className="mx-auto space-y-3">
						<h1 className="mt-8 mb-4 text-3xl font-bold tracking-tight md:text-5xl">In the news</h1>
						<p className="text-muted-foreground text-base">Unlimited audio and video transcription.</p>
					</div>
				</div>
			</div>

			<div className="container mx-auto mt-16 mb-8 max-w-3xl space-y-4 md:mt-24">
				<h3 className="pb-2">Latest news</h3>
				{newsAll.map((news, index) => (
					<Fragment key={index}>
						<Link href={news.url} target="_blank" rel="noopener noreferrer nofollow" className="group flex w-full flex-col gap-2">
							<p className="text-foreground/90 font-medium group-hover:underline group-hover:underline-offset-4">{news.title}</p>
							<span className="text-muted-foreground text-sm">{format(news.publishedAt, "MMM d, yyyy")}</span>
						</Link>
						{index < newsAll.length - 1 && <Separator className="" />}
					</Fragment>
				))}
			</div>

			<div className="container mx-auto mt-8 mb-32 max-w-3xl space-y-4">
				<Accordion type="single" collapsible className="w-full space-y-2">
					<AccordionItem
						value="1"
						className="has-focus-visible:border-ring has-focus-visible:ring-ring/50 bg-muted rounded-md border px-4 py-1 outline-hidden last:border-b has-focus-visible:ring-[3px]"
					>
						<AccordionTrigger className="justify-start gap-3 py-2 text-[13px] leading-6 font-normal text-zinc-700 hover:no-underline focus-visible:ring-0 [&>svg]:-order-1">
							{WEBNAME} has also been mentioned
						</AccordionTrigger>
						<AccordionContent className="flex w-full flex-row flex-wrap items-center gap-1.5 ps-7 pb-2">
							{mentionsAll.map((mention, index) => (
								<Fragment key={index}>
									<Link
										href={mention.url}
										target="_blank"
										rel="noopener noreferrer nofollow"
										className="group text-[13px] text-zinc-800 hover:underline hover:underline-offset-4"
									>
										<p className="">{mention.name}</p>
									</Link>
									{index < mentionsAll.length - 1 && <Separator orientation="vertical" className="h-3 w-px bg-zinc-500" />}
								</Fragment>
							))}
						</AccordionContent>
					</AccordionItem>
				</Accordion>
			</div>
		</div>
	);
}
