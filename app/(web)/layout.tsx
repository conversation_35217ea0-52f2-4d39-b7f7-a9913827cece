// import { serverSideTranslation } from '@/lib/i18n'
// import { i18nConfig } from "@/i18n-config";
import { Header } from "@/components/navigate/header";
import Footer from "@/components/navigate/footer";
import FooterBadge from "@/components/navigate/footer-badge";
import { InitializeUser } from "@/components/shared/initialize-user";
import PlanDialog from "@/components/shared/plan-dialog";
import SignInDialog from "@/components/shared/sigin-in-dialog";
import { AnalyticsClarity } from "@/components/analytics/analytics-clarity";
import { AnalyticsGoolge } from "@/components/analytics/analytics-google";

// type Params = Promise<{ lang: string }>;
export default async function RootLayout({ children }: { children: React.ReactNode }) {
	// const { t } = await serverSideTranslation(lang, 'common')
	return (
		<div className="flex min-h-screen flex-col">
			<Header />
			{children}
			<Footer />
			{/* <FooterBadge /> */}
			<InitializeUser />
			<PlanDialog />
			<SignInDialog />
			<AnalyticsClarity />
			<AnalyticsGoolge />
		</div>
	);
}
