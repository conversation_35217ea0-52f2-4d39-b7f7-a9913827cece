import { MetadataRoute } from "next";
import { WEB_URL } from "@/lib/constants";

export const dynamic = "force-static";

interface SitemapEntry {
	url: string;
	lastModified?: Date;
	changeFrequency?: "monthly" | "daily" | "always" | "hourly" | "weekly" | "yearly" | "never";
	priority?: number;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const baseUrl = WEB_URL;

	return [
		{ url: baseUrl },
		{ url: baseUrl + "/terms-of-use" },
		{ url: baseUrl + "/privacy-policy" },
		{ url: baseUrl + "/changelog" },
		{ url: baseUrl + "/pricing" },
		{ url: baseUrl + "/news" },
	] as SitemapEntry[];
}
