export type newsType = {
	title: string;
	url: string;
	publishedAt: Date;
};

export const newsAll: newsType[] = [
	// {
	// 	title: "YouStylize Transforms Photos Into Magical Art with AI, Now Listed on Crunchbase",
	// 	url: "https://www.crunchbase.com/organization/youstylize",
	// 	publishedAt: new Date("2025-05-26"),
	// },
];

export type mentionType = {
	name: string;
	url: string;
};
export const mentionsAll: mentionType[] = [
	// {
	// 	name: "Biolinky",
	// 	url: "https://biolinky.co/elliot",
	// },
	// {
	// 	name: "AllMyLinks",
	// 	url: "https://allmylinks.com/ielliot",
	// },
	// {
	// 	name: "GetAllMyLinks",
	// 	url: "https://getallmylinks.com/elliot",
	// },
	// {
	// 	name: "ShippingExplorer",
	// 	url: "https://www.shippingexplorer.net/en/user/ielliot/166851",
	// },
];
