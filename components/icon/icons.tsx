import { ComponentProps } from "react";

export const Icons = {
	gitHub: ({ className }: { className?: string }) => (
		<svg
			aria-hidden="true"
			focusable="false"
			data-prefix="fab"
			data-icon="github"
			role="img"
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 0 496 512"
			className={className}
		>
			<path
				fill="currentColor"
				d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3 .3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5 .3-6.2 2.3zm44.2-1.7c-2.9 .7-4.9 2.6-4.6 4.9 .3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3 .7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3 .3 2.9 2.3 3.9 1.6 1 3.6 .7 4.3-.7 .7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3 .7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3 .7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"
			></path>
		</svg>
	),
	google: ({ className }: { className?: string }) => (
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" className={className}>
			<path
				fill="#fff"
				d="M44.59 4.21a63.28 63.28 0 0 0 4.33 120.9a67.6 67.6 0 0 0 32.36.35a57.13 57.13 0 0 0 25.9-13.46a57.44 57.44 0 0 0 16-26.26a74.33 74.33 0 0 0 1.61-33.58H65.27v24.69h34.47a29.72 29.72 0 0 1-12.66 19.52a36.16 36.16 0 0 1-13.93 5.5a41.29 41.29 0 0 1-15.1 0A37.16 37.16 0 0 1 44 95.74a39.3 39.3 0 0 1-14.5-19.42a38.31 38.31 0 0 1 0-24.63a39.25 39.25 0 0 1 9.18-14.91A37.17 37.17 0 0 1 76.13 27a34.28 34.28 0 0 1 13.64 8q5.83-5.8 11.64-11.63c2-2.09 4.18-4.08 6.15-6.22A61.22 61.22 0 0 0 87.2 4.59a64 64 0 0 0-42.61-.38"
			/>
			<path
				fill="#e33629"
				d="M44.59 4.21a64 64 0 0 1 42.61.37a61.22 61.22 0 0 1 20.35 12.62c-2 2.14-4.11 4.14-6.15 6.22Q95.58 29.23 89.77 35a34.28 34.28 0 0 0-13.64-8a37.17 37.17 0 0 0-37.46 9.74a39.25 39.25 0 0 0-9.18 14.91L8.76 35.6A63.53 63.53 0 0 1 44.59 4.21"
			/>
			<path
				fill="#f8bd00"
				d="M3.26 51.5a62.93 62.93 0 0 1 5.5-15.9l20.73 16.09a38.31 38.31 0 0 0 0 24.63q-10.36 8-20.73 16.08a63.33 63.33 0 0 1-5.5-40.9"
			/>
			<path
				fill="#587dbd"
				d="M65.27 52.15h59.52a74.33 74.33 0 0 1-1.61 33.58a57.44 57.44 0 0 1-16 26.26c-6.69-5.22-13.41-10.4-20.1-15.62a29.72 29.72 0 0 0 12.66-19.54H65.27c-.01-8.22 0-16.45 0-24.68"
			/>
			<path
				fill="#319f43"
				d="M8.75 92.4q10.37-8 20.73-16.08A39.3 39.3 0 0 0 44 95.74a37.16 37.16 0 0 0 14.08 6.08a41.29 41.29 0 0 0 15.1 0a36.16 36.16 0 0 0 13.93-5.5c6.69 5.22 13.41 10.4 20.1 15.62a57.13 57.13 0 0 1-25.9 13.47a67.6 67.6 0 0 1-32.36-.35a63 63 0 0 1-23-11.59A63.73 63.73 0 0 1 8.75 92.4"
			/>
		</svg>
	),
};

export const IconSpeaker = ({ className, fillColor = "#ffffff", ...props }: ComponentProps<"svg"> & { fillColor?: string }) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 115.33044 107.33981" {...props}>
			<path
				d="m 2046.8238,-1119.1786 c 4.9379,-19.4504 13.1043,-45.7034 57.9245,-51.2069 44.8201,-5.5036 49.1881,42.832 49.834,46.0452 0.6455,3.2133 -1.5193,15.827 -4.0643,20.2025 -2.5448,4.3755 -33.9191,30.1499 -36.4638,31.7223 -2.545,1.5725 -29.0193,5.4352 -44.7823,-4.2045 -15.7631,-9.6398 -21.3466,-27.0392 -22.4481,-42.5586 z"
				fill={fillColor}
				paintOrder="markers stroke fill"
				transform="translate(-202.83906,38.568205) translate(-1840.228,1136.0437)"
			/>
			<path
				d="m 2057.1767,-1083.3817 c -5.0804,-5.0376 -7.6912,-8.679 -10.4227,-14.5379 -3.2749,-7.0236 -3.6485,-8.6781 -3.685,-16.3114 -0.046,-9.5959 0.6768,-15.9901 7.2629,-26.4839 13.9245,-22.1861 35.9295,-36.0818 60.1919,-33.6144 11.5804,1.4453 19.2124,3.8692 25.1205,7.9779 13.2394,9.2072 22.7532,26.5688 22.7532,41.5222 0,6.8127 -2.1535,16.3751 -4.4911,19.9426 -2.3728,3.6211 -27.5009,26.7228 -33.7138,30.9946 -6.0126,4.1344 -8.2983,5.9964 -17.9977,6.5415 -16.6286,0.9346 -35.4184,-6.6095 -45.0182,-16.0312 z m 53.404,6.4831 c 2.2276,-8.8676 9.4184,-19.8953 18.6274,-25.7089 4.9188,-3.1051 15.0577,-6.6264 19.0787,-6.6264 2.9377,0 2.946,-0.028 3.6534,-7.5654 1.32,-14.0882 -3.3866,-27.1163 -13.5307,-37.4527 -9.3631,-9.5408 -21.9464,-13.7027 -38.3274,-12.6772 -10.8154,0.6773 -16.5411,2.641 -26.121,8.9578 -9.6685,6.3757 -16.4421,14.1081 -20.6806,24.677 -4.4182,11.0169 -4.8728,24.3452 -0.3904,33.5295 3.7057,7.5929 14.2134,18.0053 22.1394,21.9799 7.948,3.9859 15.1292,5.4325 25.8774,5.2123 5.1842,-0.6091 8.3827,0.8137 9.6738,-4.3259 z m -19.3166,-22.8125 c -7.5565,-1.0208 -17.8151,-9.4886 -17.8151,-14.7138 0,-2.6622 1.3573,-2.0168 5.8588,1.6878 10.6017,8.7248 26.1317,8.5527 35.2493,1.3988 4.2751,-3.3544 7.2041,-3.4757 7.6645,-0.2663 0.3986,2.7786 -7.8158,9.8958 -12.6844,11.2633 -8.1345,2.2848 -11.5897,1.5331 -18.2731,0.6302 z m -5.6507,-33.5795 c -2.603,-4.4131 2.0792,-9.2709 5.6246,-5.7255 1.7315,1.7316 1.659,4.7765 -0.5672,6.5348 -1.7599,1.3901 -3.9638,1.0448 -5.0574,-0.8093 z m 16.6332,-1.4343 c -1.4414,-3.1635 -0.1843,-5.8297 2.9675,-6.3005 2.9019,-0.4334 5.112,2.8887 3.5417,6.0383 -1.7177,3.4449 -5.0486,3.4681 -6.5092,0.2622 z m 32.6651,42.6132 c 4.3972,-4.1089 9.4759,-8.3487 11.2858,-9.422 1.8099,-1.0735 3.0755,-2.1669 2.8122,-2.4302 -0.7527,-0.7527 -13.6652,3.704 -18.1531,6.2657 -7.2126,4.1168 -16.3211,15.9126 -16.4106,21.2528 -0.028,1.7179 10.4249,-6.2845 20.4657,-15.6663 z"
				transform="translate(-202.83906,38.568205) translate(-1840.228,1136.0437)"
				fill="#ffffff"
			/>
		</svg>
	);
};
