"use client";

import React, { useEffect, useRef } from "react";
import { useState } from "react";
import { UploadIcon, X } from "lucide-react";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import loadFfmpeg from "@/lib/utils-load-ffmpeg";
import { fetchFile } from "@ffmpeg/util";
import { toast } from "sonner";
import { Dropzone, DropzoneContent, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/custom/progress";
import { formatTime } from "@/lib/utils-date";
import { Button } from "../ui/button";
import { cn } from "@/lib/utils";
import { IconMusicFile } from "../icons";
import { FILE_DURATION_LIMIT_FREE, FILE_DURATION_LIMIT_STATER, FILE_SIZE_LIMIT_MAX } from "@/lib/constants";
import { getUploadFileUrl, handleFileUploadWithProgress } from "@/lib/file/upload-file";
import { useUserStore } from "@/store/useUserStore";
import { MembershipID } from "@/@types/membership-type";
import { AuthError } from "@/@types/error";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { getFileName, isVideoFileByType } from "@/lib/file/utils-file";
import { useSession } from "@/lib/auth-client";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useRouter } from "nextjs-toploader/app";
import { Spinner } from "@/components/ui/kibo-ui/spinner";

export default function ExtractFileClient({
	submitting,
	selectFile,
	setSelectFile,
}: {
	submitting: boolean;
	selectFile: {
		fileName: string;
		fileDuration: number;
		fileUrl: string;
	} | null;
	setSelectFile: React.Dispatch<
		React.SetStateAction<{
			fileName: string;
			fileDuration: number;
			fileUrl: string;
		} | null>
	>;
}) {
	const { data: session } = useSession();
	const { user } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const router = useRouter();

	const ffmpegRef = useRef<any>(null);
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const load = async () => {
		try {
			setIsLoading(true);
			const ffmpeg_response: FFmpeg = await loadFfmpeg();
			ffmpegRef.current = ffmpeg_response;
		} catch (error) {
			console.error("Failed to load FFmpeg:", error);
			toast.error("Failed to initialize video processor");
		} finally {
			setIsLoading(false);
		}
	};
	useEffect(() => {
		load();
	}, []);

	const [uploadingFileStatus, setUploadingFileStatus] = useState<"Preparing file..." | "Uploading file..." | null>(null); //prepare file or uploading
	const [uploadingFileProgress, setUploadingFileProgress] = useState<number>(0);
	const convertToMp3 = async (file: File, fileName: string): Promise<File> => {
		const ffmpeg = ffmpegRef.current;
		// get the original file name
		const originalFileName = file.name;
		// write the file to the file system
		await ffmpeg.writeFile(originalFileName, await fetchFile(file));
		// transcode the file
		await ffmpeg.exec(["-i", originalFileName, "-f", `mp3`, "-vn", `${fileName}.mp3`]);
		const data = await ffmpeg.readFile(`${fileName}.mp3`);
		// const url = URL.createObjectURL(new Blob([data.buffer], { type: "audio/*" }));
		// const link = document.createElement("a");
		// link.download = `${fileName}.mp3`;
		// link.href = url;
		// link.click();
		return new File([new Blob([data.buffer], { type: "audio/*" })], `${fileName}.mp3`, { type: "audio/mpeg" });
	};
	const handleLocalFileDrop = async (files: File[]) => {
		if (uploadingFileStatus) return;
		if (!session?.user) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;
		let file = files[0];

		if (file.size > FILE_SIZE_LIMIT_MAX) {
			toast.error("File size greater than 2GB, please upload a smaller file.");
			return;
		}
		try {
			setSelectFile(null);

			// 1. Get file duration & file name
			const duration = await new Promise<number>((resolve, reject) => {
				const audio = new Audio();
				const url = URL.createObjectURL(file);
				audio.src = url;
				audio.onloadedmetadata = () => {
					const duration = Number(audio.duration.toFixed(2));
					URL.revokeObjectURL(url); // Clean up the URL
					resolve(duration);
				};

				audio.onerror = (error) => {
					URL.revokeObjectURL(url);
					reject(new Error("Error loading audio file"));
				};
			});
			if (process.env.NODE_ENV === "development") {
				console.log("duration:", duration);
				console.log("file.name:", file.name);
				console.log("file.type:", file.type);
			}
			const fileName = getFileName(file.name);

			// 2. Check duration limits
			if ((!user || user.membershipId === MembershipID.Free) && duration > FILE_DURATION_LIMIT_FREE) {
				toast.error("File duration greater than 30 minutes, upgrade to upload file up to 5 hours.", {
					action: {
						label: "Upgrade",
						onClick: () => {
							setPlanBoxOpen(true);
						},
					},
				});
				return;
			}
			if (user && user.membershipId === MembershipID.Starter && duration > FILE_DURATION_LIMIT_STATER) {
				toast.error("File duration greater than 2 hours, upgrade to upload file up to 5 hours.", {
					action: {
						label: "Upgrade",
						onClick: () => {
							router.push("/user/my-subscriptions");
						},
					},
				});
				return;
			}

			// 3. If video file, convert to mp3
			if (isVideoFileByType(file.type)) {
				setUploadingFileStatus("Preparing file...");
				file = await convertToMp3(file, fileName);
			}

			// 4. Proceed with file upload
			setUploadingFileStatus("Uploading file...");
			setUploadingFileProgress(0);
			// const {
			// 	uploadUrl: uploadFileUrl,
			// 	method: uploadFileMethod,
			// 	file_url,
			// } = await getUploadFileUrl(file, !user || user.membershipId === MembershipID.Free);
			// await handleFileUploadWithProgress({
			// 	file: file,
			// 	uploadUrl: uploadFileUrl,
			// 	method: uploadFileMethod,
			// 	setUploadFileProgress: setUploadingFileProgress,
			// });

			// Set file info
			const file_url = "https://static.unoscribe.com/dev/source-free/202506/20250617-01977e62e28b778580d2f308ec5e33bd.mp3";
			setSelectFile({
				fileName: fileName,
				fileDuration: duration,
				fileUrl: file_url,
			});
		} catch (error: any) {
			console.error("Failed to upload file:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Failed to upload file: ${error.message}`);
		} finally {
			setUploadingFileStatus(null);
		}
	};

	return (
		<>
			{selectFile || uploadingFileStatus ? (
				<>
					{uploadingFileStatus ? (
						<div className="flex h-[88px] flex-col items-center justify-center gap-1 rounded bg-white p-4">
							<p className="text-muted-foreground text-sm">{uploadingFileStatus}</p>
							<div className="flex w-full flex-row items-center justify-center gap-1 text-xs">
								{uploadingFileStatus === "Uploading file..." ? (
									<>
										<Progress value={uploadingFileProgress} className="h-1.5 max-w-[280px] bg-zinc-200" indicatorClassName="bg-zinc-800" />
										<span className="font-mono tabular-nums">{uploadingFileProgress}%</span>
									</>
								) : (
									<div className="text-muted-foreground">
										<Spinner variant="ellipsis" />
									</div>
								)}
							</div>
						</div>
					) : (
						<Table className="">
							<TableHeader className="[&_tr]:border-b-0">
								<TableRow className="bg-zinc-50 hover:bg-zinc-50">
									<TableHead className="text-xs font-medium text-zinc-500">File</TableHead>
									<TableHead className="text-xs font-medium text-zinc-500">Duration</TableHead>
									<TableHead className="text-xs font-medium text-zinc-500"></TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								<TableRow className="bg-white hover:bg-zinc-50">
									<TableCell className="flex w-full flex-row items-center gap-1">
										<IconMusicFile className="h-5 w-5 flex-shrink-0 text-green-500" />
										<span className="font-[380] text-wrap text-zinc-800">{selectFile!.fileName}</span>
									</TableCell>
									<TableCell className="text-muted-foreground mx-auto text-xs font-[380] md:w-[100px]">
										{formatTime(selectFile!.fileDuration)}
									</TableCell>
									<TableCell className="text-muted-foreground mx-auto md:w-[20px]">
										<Button
											size="sm"
											variant="ghost"
											className={cn("text-zinc-600", submitting ? "cursor-not-allowed" : "cursor-pointer")}
											onClick={() => {
												if (!submitting) {
													setSelectFile(null);
												}
											}}
										>
											<X />
										</Button>
									</TableCell>
								</TableRow>
							</TableBody>
						</Table>
					)}
				</>
			) : (
				<div
					onClick={() => {
						if (!session) {
							setSignInBoxOpen(true);
						}
					}}
				>
					<Dropzone
						multiple={false}
						maxFiles={1}
						noClick={!session}
						onDragEnter={() => {
							if (!session) {
								setSignInBoxOpen(true);
								return;
							}
						}}
						onDrop={handleLocalFileDrop}
						accept={{
							"audio/mpeg": [],
							"audio/wav": [],
							"audio/aac": [],
							"audio/mp4": [],
							"audio/x-ms-wma": [],
							"audio/ogg": [],
							"audio/flac": [],
							"video/*": [],
						}}
						onError={console.error}
						className={cn("hover:border-primary cursor-pointer border-dashed bg-white whitespace-pre-wrap hover:bg-sky-50")}
					>
						<DropzoneEmptyState>
							<>
								<div className="flex size-8 items-center justify-center rounded-md bg-neutral-200 text-neutral-700">
									<UploadIcon size={16} />
								</div>
								<p className="my-2 w-full text-sm font-normal">
									Drag & drop a file here or <span className="text-blue-500">Browser</span>
								</p>
							</>
						</DropzoneEmptyState>
						<DropzoneContent />
					</Dropzone>
				</div>
			)}
		</>
	);
}
