"use client";

import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PricingPlan, pricingPlans } from "@/config/pricing";
import { cn } from "@/lib/utils";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Check, Circle, CircleCheck, Loader2 } from "lucide-react";
import { useState } from "react";
import { Button } from "../ui/button";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, handleError } from "@/@types/error";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { useRouter } from "nextjs-toploader/app";
import { CLICK_CHECKOUT } from "@/lib/umami-event-name";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { MembershipID, membershipMapping } from "@/@types/membership-type";

export default function PlanDialog() {
	const { planBoxOpen, setPlanBoxOpen } = usePlanBoxOpenStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const router = useRouter();

	const [currentTab, setCurrentTab] = useState("yearly");
	const [selectProduct, setSelectProduct] = useState<{
		membershipId: MembershipID;
		productId: string;
		plan: string;
	}>({
		membershipId: MembershipID.Pro,
		productId: pricingPlans[1].productId!.yearly,
		plan: `${pricingPlans[1].title}(Yearly)`,
	});
	const [isPurchasing, setIsPurchasing] = useState(false);

	const purchase = async (productId: string) => {
		console.log("New checkout");
		try {
			setIsPurchasing(true);
			const { status, message, url } = await ofetch("/api/payment/checkout", {
				method: "POST",
				body: { productId, type: "subscription" },
			});
			handleError(status, message);
			if (url) {
				router.push(url);
				// window.open(url, "_blank");
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Failed to purchase. Please try again.");
			}
			setIsPurchasing(false);
		} finally {
			// setIsPurchasing(false);
		}
	};

	return (
		<Dialog open={planBoxOpen} onOpenChange={setPlanBoxOpen}>
			<DialogContent className="gap-0 rounded-xl p-0 sm:max-w-max sm:rounded-xl">
				<DialogTitle />
				<ScrollArea className="max-h-[90vh] overflow-y-auto">
					<div className="flex flex-col items-center">
						{/* Plan */}
						<div className="flex w-full flex-col sm:flex-row">
							{/* Left side: 3-column pricing table */}
							<div className="order-2 w-full rounded-lg bg-zinc-100 p-6 pt-6 sm:order-1 sm:w-72">
								<div className="flex w-full flex-col gap-4 text-sm">
									<div className="flex w-full flex-row items-center justify-between gap-2">
										<p className="text-lg font-semibold">Supported Features</p>
									</div>
									<div className="flex w-full flex-row items-center justify-between gap-2">
										<p>Automatic transcription</p>
										<Check className="size-4 text-green-500" />
									</div>
									<div className="flex flex-row items-center justify-between gap-2">
										<p>Speaker recongnition(coming soon)</p>
										<Check className="size-4 text-green-500" />
									</div>
									<div className="flex flex-row items-center justify-between gap-2">
										<p>100+ languages</p>
										<Check className="size-4 text-green-500" />
									</div>
									<div className="flex flex-row items-center justify-between gap-2">
										<p>Transcripts export(coming soon)</p>
										<Check className="size-4 text-green-500" />
									</div>
									<div className="flex flex-row items-center justify-between gap-2">
										<p>History storage(coming soon)</p>
										<Check className="size-4 text-green-500" />
									</div>
									<div className="flex flex-row items-center justify-between gap-2">
										<p>Highest priority</p>
										<Check className="size-4 text-green-500" />
									</div>
									<div className="flex flex-row items-center justify-between gap-2">
										<p>Cancel anytime</p>
										<Check className="size-4 text-green-500" />
									</div>
								</div>
							</div>

							{/* Right side: Selection and purchase */}
							<div className="order-1 flex w-full flex-col items-center gap-6 p-5 sm:order-2 sm:w-[380px]">
								<p className="mx-auto w-full text-center text-2xl font-semibold">Choose your plan</p>
								<Tabs
									defaultValue="yearly"
									className=""
									onValueChange={(value) => {
										setCurrentTab(value);
										const currentPlan = pricingPlans.find((plan) => plan.id === selectProduct.membershipId);
										setSelectProduct((prev) => ({
											...prev,
											productId: value === "monthly" ? currentPlan!.productId!.monthly : currentPlan!.productId!.yearly,
											plan: `${currentPlan?.title}(${value === "monthly" ? "Monthly" : "Yearly"})`,
										}));
									}}
								>
									<TabsList className="rounded-full bg-zinc-200/75">
										<TabsTrigger value="monthly" className="rounded-full">
											Monthly
										</TabsTrigger>
										<TabsTrigger value="yearly" className="rounded-full">
											Yearly<span className="ml-1.5 text-sm text-blue-500">Save 40%</span>
										</TabsTrigger>
									</TabsList>
								</Tabs>
								<div className="w-full space-y-3">
									{pricingPlans.map((pricing: PricingPlan, index: number) => {
										if (pricing.free) return null;
										return (
											<div key={index} className="relative w-full">
												<button
													onClick={() => {
														setSelectProduct({
															membershipId: pricing.id,
															productId: currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly,
															plan: `${pricing.title}(${currentTab === "monthly" ? "Monthly" : "Yearly"})`,
														});
													}}
													className={cn(
														"flex w-full cursor-pointer flex-row items-center justify-between gap-2 rounded-lg border-2 px-4 py-4",
														selectProduct.productId ===
															(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) &&
															"border-blue-500 bg-blue-50",
													)}
												>
													<div className="flex flex-row items-center gap-2">
														{selectProduct.productId ===
														(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) ? (
															<CircleCheck className="size-6 text-blue-500" />
														) : (
															<Circle className="size-6 text-blue-500" />
														)}
														<div className="flex flex-col items-start">
															<p className="text-lg font-medium">{pricing.title}</p>
															<p className="text-muted-foreground -mt-1 text-sm">{pricing.features![0].description}</p>
														</div>
													</div>
													<p className="text-xl font-medium">
														{pricing.currency.symbol}
														{pricing.free || currentTab === "monthly" ? pricing.price.monthly : pricing.price.monthForYearly}
														<span className="text-[10px]">/mo</span>
													</p>
												</button>
												{pricing.badge && (
													<p className="absolute top-0 right-0 -translate-y-1/2 rounded-full bg-blue-500 px-2.5 py-0.5 text-xs font-normal text-white">
														{pricing.badge}
													</p>
												)}
											</div>
										);
									})}
								</div>

								<Button
									size="lg"
									className="h-[44px] w-full cursor-pointer rounded-full bg-blue-500 hover:bg-blue-600"
									onClick={() => {
										purchase(selectProduct.productId);
									}}
									disabled={isPurchasing}
									data-umami-event={CLICK_CHECKOUT}
									data-umami-event-membership={selectProduct.plan}
								>
									Buy Now
									{isPurchasing && <Loader2 className="h-4 w-4 animate-spin" />}
								</Button>
							</div>
						</div>
					</div>
				</ScrollArea>
			</DialogContent>
		</Dialog>
	);
}
