"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Loader2, Check, X, Info } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { pricingPlans, PricingPlan } from "@/config/pricing";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useUserPlanBoxOpenStore } from "@/store/useUserPlanBoxOpenStore";
import { Subscription } from "@/@types/subscription";
import { AuthError, handleError } from "@/@types/error";
import { useRouter } from "nextjs-toploader/app";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ofetch } from "ofetch";
import { CLICK_CHECKOUT } from "@/lib/umami-event-name";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { canChangePlan } from "@/lib/utils-membership";
import Link from "next/link";

export default function Plans({ isUserPlan = false }: { isUserPlan?: boolean }) {
	const router = useRouter();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { userPlanBoxOpen, userSubscription, setUserSubscription } = useUserPlanBoxOpenStore();

	const [subscription, setSubscription] = useState<Subscription | null>(null);

	useEffect(() => {
		if (isUserPlan) {
			setSubscription(userSubscription);
		}
	}, [isUserPlan, userSubscription]);
	useEffect(() => {
		if (!userPlanBoxOpen) {
			setUserSubscription(null);
		}
	}, [userPlanBoxOpen]);

	const [currentTab, setCurrentTab] = useState("yearly");

	const [isCancelling, setIsCancelling] = useState(false);
	const [isPurchasing, setIsPurchasing] = useState(false);
	const [purchaseProductId, setPurchaseProductId] = useState<string | null>(null);
	const [subscriptionChanged, setSubscriptionChanged] = useState(false);

	const purchase = async (productId: string) => {
		if (subscription) return;
		console.log("New subscription checkout");
		try {
			setIsPurchasing(true);
			setPurchaseProductId(productId);

			const { status, message, url } = await ofetch("/api/payment/checkout", {
				method: "POST",
				body: { productId, type: "subscription" },
			});
			if (status === 1001) {
				toast.error("You are already subscribed.");
				setIsPurchasing(false);
				return;
			}
			handleError(status, message);
			if (url) {
				router.push(url);
				// window.open(url, "_self");
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Failed to purchase. Please try again.");
			}
			setIsPurchasing(false);
			setPurchaseProductId(null);
		}
	};
	const changePlan = async (productId: string) => {
		if (!subscription) return;
		if (subscription.productId === productId) return;
		if (isPurchasing) return;
		console.log("Change subscription");

		try {
			setPurchaseProductId(productId);
			setIsPurchasing(true);
			const { status, message } = await ofetch("/api/payment/subscription", {
				method: "POST",
				body: {
					type: "change",
					subscriptionId: subscription.subscriptionId,
					productId,
				},
			});
			handleError(status, message);
			setSubscriptionChanged(true);
			toast.success("Your subscription change has been initiated.");
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Subscription change failed.");
		} finally {
			setIsPurchasing(false);
			setPurchaseProductId(null);
		}
	};

	return (
		<div className="relative flex w-full flex-col gap-8">
			<div className="flex items-center justify-center pt-8">
				<Label htmlFor="payment-schedule" className="me-3">
					Monthly
				</Label>
				<Switch
					id="payment-schedule"
					checked={currentTab === "yearly"}
					onCheckedChange={(checked) => {
						setCurrentTab(checked ? "yearly" : "monthly");
					}}
				/>
				<Label htmlFor="payment-schedule" className="relative ms-3">
					Yearly
					<span className="absolute start-auto -end-20 -top-10">
						<span className="flex items-center">
							<svg className="-me-6 h-8 w-14" width={45} height={25} viewBox="0 0 45 25" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path
									d="M43.2951 3.47877C43.8357 3.59191 44.3656 3.24541 44.4788 2.70484C44.5919 2.16427 44.2454 1.63433 43.7049 1.52119L43.2951 3.47877ZM4.63031 24.4936C4.90293 24.9739 5.51329 25.1423 5.99361 24.8697L13.8208 20.4272C14.3011 20.1546 14.4695 19.5443 14.1969 19.0639C13.9242 18.5836 13.3139 18.4152 12.8336 18.6879L5.87608 22.6367L1.92723 15.6792C1.65462 15.1989 1.04426 15.0305 0.563943 15.3031C0.0836291 15.5757 -0.0847477 16.1861 0.187863 16.6664L4.63031 24.4936ZM43.7049 1.52119C32.7389 -0.77401 23.9595 0.99522 17.3905 5.28788C10.8356 9.57127 6.58742 16.2977 4.53601 23.7341L6.46399 24.2659C8.41258 17.2023 12.4144 10.9287 18.4845 6.96211C24.5405 3.00476 32.7611 1.27399 43.2951 3.47877L43.7049 1.52119Z"
									fill="currentColor"
									className="text-blue-500"
								/>
							</svg>
							<span className="mt-3 font-semibold text-blue-500 uppercase">Save 40%</span>
						</span>
					</span>
				</Label>
			</div>

			{subscriptionChanged && (
				<div className="absolute z-20 w-full">
					<Alert className="mx-auto max-w-xl items-center bg-green-50 shadow-xl">
						{/* <RocketIcon className="h-4 w-4" /> */}
						<AlertTitle className="text-lg text-green-600">Your subscription change has been initiated.</AlertTitle>
						<AlertDescription className="text-muted-foreground">
							Your order is being processed. It should be completed in 20 seconds to 2 minutes. You can{" "}
							<span className="text-black italic">refresh the page to check the status</span>.
						</AlertDescription>
					</Alert>
				</div>
			)}

			<div
				className={cn(
					"mx-auto grid w-full max-w-4xl grid-cols-1 justify-center gap-6 text-start sm:grid-cols-2 md:grid-cols-3",
					// !isUserPlan && "md:grid-cols-3"
				)}
			>
				{pricingPlans.map((pricing: PricingPlan, index: number) => {
					if (isUserPlan && pricing.free) return null;
					return (
						<Card
							key={index}
							className={cn(
								"bg-muted relative mx-auto w-full max-w-xs rounded-lg shadow-none",
								pricing.badge && "border border-blue-500 bg-blue-50",
							)}
						>
							<CardHeader className="">
								<CardTitle className="flex flex-row items-center justify-between gap-1 text-xl font-medium">{pricing.title}</CardTitle>
							</CardHeader>

							<CardContent className="flex flex-col gap-8">
								<div className="flex flex-wrap items-end">
									<span className="text-4xl font-medium tabular-nums">
										{pricing.currency.symbol}
										{pricing.free || currentTab === "monthly" ? pricing.price.monthly : pricing.price.monthForYearly}
									</span>
									<div className="mb-1 ml-2 text-xs leading-none">
										{/* {!pricing.free && currentTab === "yearly" && (
											<span className="text-neutral-600">
												<span className="text-base line-through">{pricing.price.monthly}</span>
											</span>
										)} */}
										<p className="font-semibold text-neutral-800">{!pricing.free && pricing.duration}</p>
									</div>
								</div>

								{pricing.free ? (
									<Link href="/" className={cn(buttonVariants({ variant: "outline" }), "w-full max-w-xs")}>
										Try Now
									</Link>
								) : !subscription ? (
									<Button
										{...{
											variant: pricing.badge ? "default" : "outline",
											disabled:
												isPurchasing &&
												purchaseProductId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly),
										}}
										className={cn("w-full", pricing.badge && "bg-blue-600 hover:bg-blue-500")}
										onClick={() => {
											purchase(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly);
										}}
										data-umami-event={CLICK_CHECKOUT}
										data-umami-event-membership={`${pricing.title}(${currentTab === "monthly" ? "Monthly" : "Yearly"})`}
									>
										<p className="flex flex-row items-center space-x-1">
											<span>Get Started</span>
											{isPurchasing &&
												purchaseProductId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) && (
													<Loader2 className="h-4 w-4 animate-spin" />
												)}
										</p>
									</Button>
								) : subscription.productId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) ? (
									<Button
										{...{
											variant: pricing.badge ? "default" : "outline",
										}}
										disabled
										className={cn("w-full", pricing.badge && "bg-blue-600 hover:bg-blue-500")}
									>
										<p className="flex flex-row items-center space-x-1">Current plan</p>
									</Button>
								) : (
									<AlertDialog>
										<AlertDialogTrigger asChild>
											<Button
												{...{
													variant: pricing.badge ? "default" : "outline",
													disabled:
														(isPurchasing &&
															purchaseProductId ===
																(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly)) ||
														!canChangePlan(
															subscription.productId,
															currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly,
														),
												}}
												className={cn("w-full", pricing.badge && "bg-blue-600 hover:bg-blue-500")}
											>
												<p className="flex flex-row items-center space-x-1">
													<span>Change</span>
													{isPurchasing &&
														purchaseProductId ===
															(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) && (
															<Loader2 className="h-4 w-4 animate-spin" />
														)}
												</p>
											</Button>
										</AlertDialogTrigger>
										<AlertDialogContent>
											<AlertDialogHeader>
												<AlertDialogTitle>Are you sure you want to change your subscription plan?</AlertDialogTitle>
												<AlertDialogDescription>
													Your plan will change right away. We’ll adjust your bill to match your new plan.
												</AlertDialogDescription>
											</AlertDialogHeader>
											<AlertDialogFooter>
												<AlertDialogCancel>Cancel</AlertDialogCancel>
												<AlertDialogAction
													onClick={() => {
														changePlan(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly);
													}}
												>
													Change
												</AlertDialogAction>
											</AlertDialogFooter>
										</AlertDialogContent>
									</AlertDialog>
								)}

								<div className="flex flex-col gap-4">
									<div className="flex flex-col gap-3 text-sm text-gray-700">
										{pricing.features?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-3">
												<Check className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-700" />
												<span className="inline-flex items-center gap-1">
													{feature.description}
													{feature.tips && (
														<TooltipProvider delayDuration={100}>
															<Tooltip>
																<TooltipTrigger>
																	<Info className="text-muted-foreground h-4 w-4" />
																</TooltipTrigger>
																<TooltipContent className="bg-black">
																	<p style={{ whiteSpace: "pre-wrap" }}>{feature.tips}</p>
																</TooltipContent>
															</Tooltip>
														</TooltipProvider>
													)}
												</span>
											</p>
										))}
										{/* {pricing.unFeatures?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-3">
												<X className="mt-0.5 h-4 w-4 flex-shrink-0 text-red-700" />
												<span className="">{feature}</span>
											</p>
										))} */}
										{pricing.unFeatures?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-3">
												<X className="mt-0.5 h-4 w-4 flex-shrink-0 text-red-700" />
												<span className="inline-flex items-center gap-1">
													{feature.description}
													{feature.tips && (
														<TooltipProvider delayDuration={100}>
															<Tooltip>
																<TooltipTrigger>
																	<Info className="text-muted-foreground h-4 w-4" />
																</TooltipTrigger>
																<TooltipContent className="bg-black">
																	<p style={{ whiteSpace: "pre-wrap" }}>{feature.tips}</p>
																</TooltipContent>
															</Tooltip>
														</TooltipProvider>
													)}
												</span>
											</p>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
					);
				})}
			</div>
		</div>
	);
}
