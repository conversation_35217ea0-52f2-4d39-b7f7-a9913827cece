"use client";

import { useState } from "react";
import { Check, Info } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { PricingOnetime, pricingOnetime, pricingOnetimeFeatures } from "@/config/pricing";
import { Card, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AuthError, handleError } from "@/@types/error";
// import { useRouter } from "nextjs-toploader/app";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ofetch } from "ofetch";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ORDER_PRODUCT_ID_2 } from "@/lib/utils-membership";
import { SubmitButton } from "../ui/submit-button";
import { CLICK_CHECKOUT } from "@/lib/umami-event-name";

export default function Plans() {
	// const router = useRouter();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();

	const [selectProductId, setSelectProductId] = useState<string>(ORDER_PRODUCT_ID_2);
	const [isPurchasing, setIsPurchasing] = useState(false);
	const purchase = async (productId: string) => {
		console.log("New checkout");
		try {
			setIsPurchasing(true);
			const { status, message, url } = await ofetch("/api/payment/checkout", {
				method: "POST",
				body: { productId, type: "onetime" },
			});
			handleError(status, message);
			if (url) {
				// router.push(url);
				window.open(url, "_blank");
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Failed to purchase. Please try again.");
			}
		} finally {
			setIsPurchasing(false);
		}
	};

	return (
		<div className="grid grid-cols-1 flex-col gap-6 sm:grid-cols-2 md:gap-8">
			<Card
				className={cn(
					"relative w-full rounded-xl border-none bg-muted shadow-none",
					// pricing.buttonHighlighted && "bg-gray-100"
				)}
			>
				<CardHeader className="flex flex-col gap-1.5 text-sm">
					{pricingOnetimeFeatures?.map((feature: any, index: any) => (
						<p key={index} className="flex items-start gap-3">
							<Check className="mt-0.5 h-4 w-4 shrink-0 text-teal-500" />
							<span className="inline-flex items-center gap-1">
								{feature.description}
								{feature.tips && (
									<TooltipProvider delayDuration={100}>
										<Tooltip>
											<TooltipTrigger>
												<Info className="h-4 w-4 text-muted-foreground" />
											</TooltipTrigger>
											<TooltipContent className="bg-black">
												<p style={{ whiteSpace: "pre-wrap" }}>{feature.tips}</p>
											</TooltipContent>
										</Tooltip>
									</TooltipProvider>
								)}
							</span>
						</p>
					))}
				</CardHeader>
			</Card>
			<div className="flex w-full flex-col gap-3">
				{pricingOnetime.map((pricing: PricingOnetime, index: number) => (
					<Card
						key={index}
						className={cn("w-full cursor-pointer rounded-xl shadow-none", selectProductId === pricing.productId && "border-2 border-neutral-900")}
						onClick={() => setSelectProductId(pricing.productId)}
					>
						{pricing.badge && (
							<Badge
								variant="default"
								className="flex w-full justify-center rounded-none rounded-t-lg border-none bg-neutral-900 font-medium hover:bg-neutral-900"
							>
								{pricing.badge}
							</Badge>
							// <div className="flex w-full items-center rounded-t-xl bg-teal-600 px-2.5 py-0.5 text-xs font-normal transition-colors hover:bg-teal-600 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2">
							// 	{pricing.badge}
							// </div>
						)}
						<CardHeader className="flex flex-col gap-1 p-3">
							<div className="flex flex-row items-center justify-between gap-1">
								<p className={cn(selectProductId === pricing.productId ? "font-medium" : "font-normal")}>
									{pricing.credits} credits <span className="text-sm font-normal text-muted-foreground">({pricing.credits} photos)</span>
								</p>
								<span className="">
									{pricing.currency.symbol}
									{pricing.price}
								</span>
							</div>
						</CardHeader>
					</Card>
				))}
				<SubmitButton
					isSubmitting={isPurchasing}
					size="lg"
					className="bg-teal-500 hover:bg-teal-600"
					onClick={() => purchase(selectProductId)}
					data-umami-event={CLICK_CHECKOUT}
				>
					Purchase
				</SubmitButton>
			</div>
		</div>
	);
}
