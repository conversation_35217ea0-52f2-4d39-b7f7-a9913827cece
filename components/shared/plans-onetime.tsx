"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Loader2, Check, X, Info } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { pricingOnetime, PricingOnetime } from "@/config/pricing";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AuthError, handleError } from "@/@types/error";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ofetch } from "ofetch";
import { CLICK_CHECKOUT } from "@/lib/umami-event-name";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { useRouter } from "nextjs-toploader/app";
// import { PolarEmbedCheckout } from "@polar-sh/checkout/embed";
// import { useSession } from "@/lib/auth-client";

export default function Plans({ hasFree = false }: { hasFree?: boolean }) {
	// const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const router = useRouter();

	const [isPurchasing, setIsPurchasing] = useState(false);
	const [purchaseProductId, setPurchaseProductId] = useState<string | null>(null);

	// useEffect(() => {
	// 	PolarEmbedCheckout.init();
	// }, []);

	const purchase = async (productId: string) => {
		console.log("New checkout");
		try {
			setPurchaseProductId(productId);
			setIsPurchasing(true);
			const { status, message, url } = await ofetch("/api/payment/checkout", {
				method: "POST",
				body: { productId, type: "onetime" },
			});
			handleError(status, message);
			if (url) {
				router.push(url);
				// window.open(url, "_blank");
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Failed to purchase. Please try again.");
			}
		} finally {
			// setIsPurchasing(false);
		}
	};

	return (
		<div className="relative flex w-full flex-col gap-8">
			<div
				className={cn(
					"mx-auto grid w-full grid-cols-1 justify-center gap-6 text-start sm:grid-cols-2 md:grid-cols-4",
					// hasFree ? "lg:grid-cols-4" : "max-w-4xl",
				)}
			>
				{pricingOnetime.map((pricing: PricingOnetime, index: number) => {
					// if (pricing.free && !hasFree) return null;
					return (
						<Card
							key={index}
							className={cn(
								"bg-muted relative mx-auto w-full max-w-xs rounded shadow-none",
								pricing.badge && "border border-teal-500 bg-teal-50",
							)}
						>
							{/* {pricing.badge && (
								<Badge
									variant="default"
									className="absolute left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-linear-to-r from-teal-600 to-teal-400 font-normal hover:bg-primary/80"
								>
									{pricing.badge}
								</Badge>
							)} */}
							<CardHeader>
								<CardTitle className="flex flex-row items-center justify-between gap-1 text-xl font-medium">
									{pricing.free ? `Free (${pricing.credits} credits)` : `${pricing.credits} credits`}
								</CardTitle>
							</CardHeader>

							<CardContent className="flex flex-col gap-8">
								<div className="flex flex-wrap items-end">
									<span className="text-4xl font-medium">
										{pricing.currency.symbol}
										{pricing.price}
									</span>
								</div>

								{pricing.free ? (
									<NoPrefetchLink href="/" className={cn(buttonVariants({ variant: "outline" }), "w-full")}>
										<p className="flex flex-row items-center space-x-1">
											<span>Get Started</span>
										</p>
									</NoPrefetchLink>
								) : (
									// <a
									// 	href={`${pricing.checkoutLink}${session ? `?customer_email=${session.user.email}&customer_name=${session.user.name}` : ""}`}
									// 	data-polar-checkout
									// 	data-polar-checkout-theme="light"
									// 	className={cn(buttonVariants({ variant: pricing.badge ? "default" : "outline" }), "w-full")}
									// 	data-umami-event={CLICK_CHECKOUT}
									// 	data-umami-event-price={`${pricing.currency.symbol}${pricing.price}`}
									// >
									// 	Purchase
									// </a>
									<Button
										{...{
											variant: pricing.badge ? "default" : "outline",
											disabled: isPurchasing && purchaseProductId === pricing.productId,
										}}
										className={cn("w-full", pricing.badge && "bg-teal-600 hover:bg-teal-500")}
										onClick={() => {
											purchase(pricing.productId);
										}}
										data-umami-event={CLICK_CHECKOUT}
										data-umami-event-price={`${pricing.currency.symbol}${pricing.price}`}
									>
										<p className="flex flex-row items-center space-x-1">
											<span>Purchase</span>
											{isPurchasing && purchaseProductId === pricing.productId && <Loader2 className="h-4 w-4 animate-spin" />}
										</p>
									</Button>
								)}

								<div className="flex flex-col gap-4">
									<div className="flex flex-col gap-3 text-sm text-gray-700">
										{pricing.features?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-3">
												<Check className="mt-0.5 h-4 w-4 shrink-0 text-green-700" />
												<span className="inline-flex items-center gap-1">
													{feature.description}
													{feature.tips && (
														<TooltipProvider delayDuration={100}>
															<Tooltip>
																<TooltipTrigger>
																	<Info className="text-muted-foreground h-4 w-4" />
																</TooltipTrigger>
																<TooltipContent className="bg-black">
																	<p style={{ whiteSpace: "pre-wrap" }}>{feature.tips}</p>
																</TooltipContent>
															</Tooltip>
														</TooltipProvider>
													)}
												</span>
											</p>
										))}

										{pricing.unFeatures?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-3">
												<X className="mt-0.5 h-4 w-4 shrink-0 text-red-700" />
												<span className="">{feature}</span>
											</p>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
					);
				})}
			</div>
		</div>
	);
}
