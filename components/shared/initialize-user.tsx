"use client";

import { useEffect } from "react";
import { useUserStore } from "@/store/useUserStore";
import { useCookies } from "next-client-cookies";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSession } from "@/lib/auth-client";

export function InitializeUser() {
	const { data: session, isPending } = useSession();
	const cookies = useCookies();
	const { user, refreshUser } = useUserStore();
	const { planBoxOpen, setPlanBoxOpen } = usePlanBoxOpenStore();

	useEffect(() => {
		console.log("isPending:", isPending);
		if (isPending) return;
		if (session && !user) {
			refreshUser();
		}
	}, [isPending]);

	return <></>;
}
