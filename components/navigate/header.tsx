"use client";

import React, { useState } from "react";
import { ChevronDown, CreditCard, MenuIcon, Power, Icon, History, ArrowRight, Crown } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Logo } from "@/components/logo";
import { useSession } from "@/lib/auth-client";
import { WEBNAME } from "@/lib/constants";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import {
	NavigationMenu,
	NavigationMenuContent,
	NavigationMenuItem,
	NavigationMenuLink,
	NavigationMenuList,
	NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUserStore } from "@/store/useUserStore";
import { MembershipID, membershipMapping } from "@/@types/membership-type";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignOut } from "@/hooks/use-signout";
import { toast } from "sonner";
import { AuthError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "../ui/submit-button";
import { OPEN_PLAN } from "@/lib/umami-event-name";
import Link from "next/link";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

interface MenuItem {
	name: string;
	href?: string;
	prefetch?: boolean;
	icon?: React.ReactNode;
	target?: string;
	hidden?: boolean;
	items?: { name: string; href: string; prefetch?: boolean; description?: string; image?: string; next?: boolean }[];
}

const menuItems: MenuItem[] = [
	{ name: "Home", href: "/" },
	// { name: "Blog", href: "/blog", prefetch: false },
	{ name: "Pricing", href: "/pricing" },
];

export const Header = () => {
	const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const { handleSignOut } = useSignOut();
	const { user, setUser, refreshUser } = useUserStore();

	const [showMobileMenu, setShowMobileMenu] = useState<boolean>(false);

	const [refreshingUserCredits, setRefreshingUserCredits] = useState<boolean>(false);
	const handleRefreshUserCredits = async () => {
		if (!session?.user) {
			return;
		}

		try {
			setRefreshingUserCredits(true);
			await refreshUser();
		} catch (error: any) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			toast.error("Network error");
		} finally {
			setRefreshingUserCredits(false);
		}
	};

	return (
		<header className="sticky top-0 z-20 w-full border-b border-transparent bg-white px-2 transition-colors duration-300">
			<div className="flex h-16 flex-wrap items-center justify-between px-4 md:px-6">
				<div className="-ml-4 flex flex-row items-center gap-8 md:-ml-0">
					<Link href="/" className="flex items-center gap-2 rtl:space-x-reverse">
						<Logo />
						<span className="text-xl font-semibold">{WEBNAME}</span>
					</Link>
					<div className="hidden rounded-lg font-normal md:flex md:flex-row">
						<NavigationMenu>
							<NavigationMenuList className="space-x-0">
								{menuItems.map((route, index) => (
									<React.Fragment key={index}>
										{route.items ? (
											<NavigationMenuItem>
												<NavigationMenuTrigger className="bg-transparent px-3 font-normal text-black/70 hover:bg-transparent focus:bg-transparent data-active:bg-transparent data-[state=open]:bg-transparent">
													{route.name}
												</NavigationMenuTrigger>
												<NavigationMenuContent>
													<div className="space-y-2 px-2 py-4">
														<p className="text-muted-foreground pl-3 text-sm">{route.name}</p>
														<div className="flex w-[280px] flex-col gap-0.5">
															{route.items.map((feature, index) => (
																<Link
																	key={index}
																	href={feature.href}
																	prefetch={feature.prefetch ?? true}
																	className="text-muted-foreground flex flex-row items-center rounded-lg px-1 py-0.5 hover:bg-zinc-100"
																>
																	{feature.image && (
																		<img
																			src={feature.image}
																			alt={feature.name}
																			className="aspect-square h-16 rounded-lg object-cover"
																			loading="lazy"
																		/>
																	)}
																	<div className="p-2">
																		<p className="text-sm text-black">{feature.name}</p>
																		{feature.description && (
																			<p className="text-muted-foreground text-xs">{feature.description}</p>
																		)}
																	</div>
																	{feature.next && <ArrowRight className="text-muted-foreground mr-2 ml-auto h-4 w-4" />}
																</Link>
															))}
														</div>
													</div>
												</NavigationMenuContent>
											</NavigationMenuItem>
										) : (
											<NavigationMenuItem>
												<NavigationMenuLink
													className={cn("hover:text-accent-foreground px-3 py-2 text-sm hover:bg-transparent")}
													asChild
												>
													<Link
														href={route.href!}
														prefetch={route.prefetch ?? true}
														className="flex flex-row items-center font-normal text-black/70"
														target={route.target}
													>
														{route.name}
														{route.icon && <>{route.icon}</>}
													</Link>
												</NavigationMenuLink>
											</NavigationMenuItem>
										)}
									</React.Fragment>
								))}
							</NavigationMenuList>
						</NavigationMenu>
					</div>
				</div>

				<div className="flex flex-row items-center gap-3">
					{session ? (
						<>
							{user?.membershipId === MembershipID.Free && (
								<Button size="sm" className="cursor-pointer text-xs" onClick={() => setPlanBoxOpen(true)}>
									<Crown className="size-3.5 fill-current text-yellow-500" />
									Go unlimited
								</Button>
							)}
							<DropdownMenu modal={false}>
								<DropdownMenuTrigger asChild className="cursor-pointer">
									<div className="flex shrink-0 flex-row items-center gap-2">
										<Avatar className="h-7 w-7">
											<AvatarImage src={session?.user.image!} alt="User Avatar" />
											<AvatarFallback>{session?.user.name}</AvatarFallback>
										</Avatar>
										<ChevronDown className="size-3.5 shrink-0 text-neutral-600 lg:block" />
									</div>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[280px] p-0" align="end" forceMount>
									<div className="flex gap-5 p-5">
										<Avatar className="flex size-9 shrink-0 items-center gap-2 text-neutral-800">
											<AvatarImage src={session?.user.image!} alt="User Avatar" />
											<AvatarFallback>{session?.user.name}</AvatarFallback>
										</Avatar>
										<div className="flex min-w-0 flex-1 flex-col items-start">
											<p className="truncate text-sm font-semibold">{session?.user.name ?? WEBNAME}</p>
											<p className="text-muted-foreground mt-1 truncate text-xs">{session?.user.email ?? "--"}</p>
										</div>
									</div>
									{user?.membershipId === MembershipID.Free && (
										<div className="px-[24px] pb-5">
											<Button
												size="sm"
												className="w-full cursor-pointer bg-blue-500 hover:bg-blue-600"
												onClick={() => setPlanBoxOpen(true)}
												data-umami-event={OPEN_PLAN}
											>
												{/* Get a plan */}Go unlimited
											</Button>
										</div>
									)}

									<Separator />

									<NoPrefetchLink
										href="/user/my-subscriptions"
										className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
									>
										<div className="flex flex-row items-center">
											<CreditCard className="mr-5 h-4 w-4 shrink-0" />
											Subscription
										</div>
										{user && (
											<p className="flex items-center gap-1 rounded bg-zinc-200 px-2 py-1 text-xs font-medium">
												{membershipMapping[user.membershipId as MembershipID].name}
											</p>
										)}
									</NoPrefetchLink>
									{/* <NoPrefetchLink
										href="/user/my-creations"
										className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
									>
										<div className="flex flex-row items-center">
											<History className="mr-5 h-4 w-4 shrink-0" />
											My Creations
										</div>
									</NoPrefetchLink> */}
									{/* <PrefetchLink
										href={FEEDBACK_URL}
										target="_blank"
										className="flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100 hover:bg-muted"
									>
										<MessageCircle className="mr-5 h-4 w-4 shrink-0" />
										Got Feedback
									</PrefetchLink> */}

									<Separator />

									<button
										className="hover:bg-muted flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100"
										onClick={handleSignOut}
									>
										<Power className="mr-5 h-4 w-4 shrink-0" />
										Sign out
									</button>
								</DropdownMenuContent>
							</DropdownMenu>
						</>
					) : (
						<Button size="sm" className="cursor-pointer" onClick={() => setSignInBoxOpen(true)}>
							Sign In
						</Button>
					)}
					{/* <button className="-mr-4 flex md:hidden cursor-pointer" onClick={() => setShowMobileMenu(true)}>
						<MenuIcon className="size-6 md:hidden" />
					</button> */}
				</div>
			</div>

			<Dialog open={showMobileMenu} onOpenChange={setShowMobileMenu}>
				<DialogContent className="h-[98dvh] w-[98dvw] max-w-full rounded">
					<DialogHeader>
						<DialogTitle className="text-start">
							<Link href="/" className="flex items-center space-x-2">
								<Logo />
								<span className="text-lg font-semibold">{WEBNAME}</span>
							</Link>
						</DialogTitle>
						<div className="h-full pt-3 text-start">
							{menuItems.map((route, index) => (
								<React.Fragment key={index}>
									{route.items ? (
										<div className="space-y-2">
											<Accordion type="single" collapsible>
												<AccordionItem value="item-1">
													<AccordionTrigger className="py-3 text-base font-normal text-black/80 hover:no-underline">
														{route.name}
													</AccordionTrigger>
													<AccordionContent className="space-y-2">
														{route.items.map((route, index) => (
															<div key={index} className="">
																<Link
																	href={route.href}
																	prefetch={route.prefetch ?? true}
																	className="flex flex-row items-center text-black/70"
																	onClick={() => setShowMobileMenu(false)}
																>
																	<p className="items-center">{route.name}</p>
																	{route.next && <ArrowRight className="ml-auto h-3.5 w-3.5" />}
																</Link>
															</div>
														))}
													</AccordionContent>
												</AccordionItem>
											</Accordion>
										</div>
									) : (
										<div className="">
											<div className="py-3">
												<Link
													href={route.href!}
													prefetch={route.prefetch ?? true}
													className="font-normal text-black/80"
													target={route.target}
													onClick={() => setShowMobileMenu(false)}
												>
													<p className="items-center">
														{route.name}
														{route.icon && <>{route.icon}</>}
													</p>
												</Link>
											</div>
											<Separator className="" />
										</div>
									)}
								</React.Fragment>
							))}
						</div>
						{/* <DialogFooter>
							{session ? (
								<PrefetchLink href={ROUTE_PATH_SIGN_IN} className={cn(buttonVariants(), "mx-auto")}>
									Dashboard
								</PrefetchLink>
							) : (
								<PrefetchLink href={ROUTE_PATH_SIGN_IN} className={cn(buttonVariants(), "mx-auto")}>
									Get Started
								</PrefetchLink>
							)}
						</DialogFooter> */}
					</DialogHeader>
				</DialogContent>
			</Dialog>
		</header>
	);
};
