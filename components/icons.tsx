import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

export const IconMusicFile = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		// <svg viewBox="-1.5 -1.5 18.00 18.00" xmlns="http://www.w3.org/2000/svg" className={cn("text-inherit", className)}>
		// 	<g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
		// 	<g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
		// 	<g id="SVGRepo_iconCarrier">
		// 		<path
		// 			d="M7 10C7 9.44771 6.55228 9 6 9C5.44772 9 5 9.44771 5 10C5 10.5523 5.44772 11 6 11C6.55228 11 7 10.5523 7 10Z"
		// 			fill="currentColor"
		// 		></path>
		// 		<path
		// 			fill="currentColor"
		// 			fillRule="evenodd"
		// 			clipRule="evenodd"
		// 			d="M1 1.5C1 0.671573 1.67157 0 2.5 0H10.7071L14 3.29289V13.5C14 14.3284 13.3284 15 12.5 15H2.5C1.67157 15 1 14.3284 1 13.5V1.5ZM7.34189 4.02569C7.54606 3.95763 7.77087 4.02786 7.9 4.20003L8.2 4.60003C8.86099 5.48135 9.89835 6.00003 11 6.00003V7.00003C9.88299 7.00003 8.8174 6.58529 8 5.8542V10C8 11.1046 7.10457 12 6 12C4.89543 12 4 11.1046 4 10C4 8.89543 4.89543 8 6 8C6.36429 8 6.70583 8.09739 7 8.26756V4.50003C7 4.28482 7.13772 4.09375 7.34189 4.02569Z"
		// 		></path>
		// 	</g>
		// </svg>
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" className={cn("text-inherit", className)}>
			<path fill="currentColor" d="M7 10a1 1 0 1 0-2 0a1 1 0 0 0 2 0" />
			<path
				fill="currentColor"
				fillRule="evenodd"
				d="M1 1.5A1.5 1.5 0 0 1 2.5 0h8.207L14 3.293V13.5a1.5 1.5 0 0 1-1.5 1.5h-10A1.5 1.5 0 0 1 1 13.5zm6.342 2.526A.5.5 0 0 1 7.9 4.2l.3.4A3.5 3.5 0 0 0 11 6v1a4.5 4.5 0 0 1-3-1.146V10a2 2 0 1 1-1-1.732V4.5a.5.5 0 0 1 .342-.474"
				clipRule="evenodd"
			/>
		</svg>
	);
};
