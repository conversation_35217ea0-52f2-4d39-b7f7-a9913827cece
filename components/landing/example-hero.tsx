import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

export function ExampleHero({
	title,
	description,
	images,
	className,
}: {
	title?: string;
	description?: string;
	images: {
		style: string;
		url: string;
	}[];
} & ComponentProps<"div">) {
	return (
		<div className={cn("container flex flex-col items-center gap-16 px-6 py-20", className)}>
			{title && (
				<div className="text-center">
					<h2 className="text-pretty text-[32px] font-semibold">{title}</h2>
					{description && <p className="mx-auto mt-4 max-w-4xl text-pretty text-muted-foreground">{description}</p>}
				</div>
			)}

			<div className="grid w-full grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4">
				{images.map((image, index) => (
					<div key={index} className="group relative overflow-hidden rounded-lg">
						<img
							src={image.url}
							alt=""
							className="aspect-square h-full w-full rounded-lg object-cover transition-all duration-300 hover:scale-105"
							loading="lazy"
						/>
						<p className="absolute left-2 top-2 hidden rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white transition-all duration-300 group-hover:block">
							{image.style}
						</p>
					</div>
				))}
			</div>
		</div>
	);
}
