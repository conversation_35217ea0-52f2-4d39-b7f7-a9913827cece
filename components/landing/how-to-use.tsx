"use client";

import React, { ComponentProps } from "react";
import { cn } from "@/lib/utils";

export function HowToUse({
	title,
	description,
	steps,
	...props
}: {
	title?: string;
	description?: string;
	steps: {
		title: string;
		description?: string;
		url?: string;
	}[];
} & ComponentProps<"div">) {
	return (
		<div className="container flex flex-col items-center gap-16 px-6 py-20">
			<div className="text-center">
				<h2 className="text-pretty text-[32px] font-semibold">{title}</h2>
				{description && <p className="mx-auto mt-4 max-w-4xl text-pretty text-muted-foreground">{description}</p>}
			</div>

			<div className={cn("grid grid-cols-1 gap-4 sm:grid-cols-2", steps.length > 3 ? "max-w-[1360px] xl:grid-cols-4" : "lg:grid-cols-3")}>
				{steps.map((step, index) => (
					<div key={index} className="flex max-w-full flex-col items-center justify-between gap-6 rounded-xl bg-neutral-100 p-6">
						<div className="space-y-4">
							<h3 className="flex flex-col gap-2 text-xl font-semibold">
								{!step.url && <p className="font-medium text-teal-500">Step {index + 1}</p>}
								<p>{step.title}</p>
							</h3>
							<p className="text-sm text-muted-foreground">{step.description}</p>
						</div>
						{step.url && (
							<div className="w-full">
								<img src={step.url} alt={step.title} className="relative w-full rounded-lg border object-cover" loading="lazy" />
							</div>
						)}
						{/* <div className="w-full">
							{step.url ? (
								<img src={step.url} alt={step.title} className="relative w-full rounded-lg border object-cover" loading="lazy" />
							) : (
								<p
									className="text-right text-6xl font-extrabold text-neutral-300 after:content-(--content) md:text-7xl"
									style={{ "--content": `"${index + 1}"` } as React.CSSProperties}
								></p>
							)}
						</div> */}
					</div>
				))}
			</div>
		</div>
	);
}
