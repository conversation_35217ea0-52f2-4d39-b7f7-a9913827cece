import { cn } from "@/lib/utils";
import { ComponentProps } from "react";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { buttonVariants } from "../ui/button";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

export function ExampleApp({
	title,
	description,
	apps,
	className,
	next,
	...props
}: {
	title?: string;
	description?: string;
	apps: {
		image: string;
		title: string;
		url: string;
	}[];
	next?: boolean;
} & ComponentProps<"div">) {
	return (
		<div className={cn("container flex flex-col items-center gap-16 px-6 py-20", className)} {...props}>
			{title && (
				<div className="text-center">
					<h2 className="text-pretty text-[32px] font-semibold">{title}</h2>
					{description && <p className="mx-auto mt-4 max-w-4xl text-pretty text-muted-foreground">{description}</p>}
				</div>
			)}

			<div className="grid w-full grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
				{apps.map((app, index) => (
					<NoPrefetchLink key={index} href={app.url} className="group rounded-xl bg-neutral-100">
						<AspectRatio ratio={3 / 2} className="overflow-hidden rounded-t-xl">
							<Image
								src={app.image}
								alt=""
								className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
								fill={true}
							/>
						</AspectRatio>
						<p className="flex flex-row items-center gap-2 px-3 py-2 text-sm md:px-4 md:py-3">{app.title}</p>
					</NoPrefetchLink>
				))}
			</div>

			{next && (
				<div className="-mt-8">
					<NoPrefetchLink
						href="/photo-effects"
						className={cn(buttonVariants({ variant: "ghost" }), "text-nowrap text-teal-500 before:content-(--content) hover:text-teal-500")}
						style={{ "--content": "'More Styles'" } as React.CSSProperties}
					>
						<ArrowRight />
					</NoPrefetchLink>
				</div>
			)}
		</div>
	);
}
