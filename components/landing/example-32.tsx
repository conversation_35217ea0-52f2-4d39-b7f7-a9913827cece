import { cn } from "@/lib/utils";
import { ComponentProps } from "react";
import { AspectRatio } from "@/components/ui/aspect-ratio";

export function Example32({
	title,
	description,
	images,
	className,
	...props
}: {
	title?: string;
	description?: string;
	images: string[];
} & ComponentProps<"div">) {
	return (
		<div className={cn("container flex flex-col items-center gap-16 px-6 py-20", className)} {...props}>
			{title && (
				<div className="text-center">
					<h2 className="text-pretty text-[32px] font-semibold">{title}</h2>
					{description && <p className="mx-auto mt-4 max-w-4xl text-pretty text-muted-foreground">{description}</p>}
				</div>
			)}

			<div className="grid w-full grid-cols-2 gap-4 md:grid-cols-4">
				{images.map((image, index) => (
					<AspectRatio key={index} ratio={3 / 2} className=" ">
						<img src={image} alt="" className="h-full w-full rounded-lg object-cover" loading="lazy" />
					</AspectRatio>
				))}
			</div>
		</div>
	);
}
