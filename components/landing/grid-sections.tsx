import { type LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

export function GridSections({
	title,
	description,
	sections,
	variant = "default",
}: {
	title?: string;
	description?: string;
	sections: {
		title: string;
		description: string;
		icon: LucideIcon;
	}[];
	variant?: "default" | "tech";
}) {
	return (
		<div className="container flex flex-col items-center gap-16 px-6 py-16">
			{title && (
				<div className="text-center">
					<h2 className="text-pretty text-[32px] font-semibold">{title}</h2>
					{description && <p className="mx-auto mt-4 max-w-4xl text-pretty text-muted-foreground">{description}</p>}
				</div>
			)}

			<div className="-mt-6 grid w-full grid-cols-1 gap-4 sm:grid-cols-2 md:-mt-10 lg:grid-cols-3">
				{sections.map((section, index) => (
					<div
						key={index}
						className={cn(
							"group flex flex-col items-center gap-6 rounded-xl p-6 transition-all duration-300 md:p-8",
							variant === "tech"
								? "border border-border/40 bg-background/50 backdrop-blur-xs hover:border-teal-500/30 hover:shadow-md hover:shadow-teal-500/5"
								: "",
						)}
					>
						<div
							className={cn(
								"flex h-14 w-14 items-center justify-center rounded-lg",
								variant === "tech" ? "bg-teal-500/10 ring-1 ring-teal-500/20 group-hover:bg-teal-500/20" : "",
							)}
						>
							<section.icon
								className={cn("h-7 w-7 text-teal-500 transition-transform duration-300", variant === "tech" ? "group-hover:scale-110" : "")}
								strokeWidth={1.5}
							/>
						</div>
						<h3 className="text-center text-base font-medium md:text-lg">{section.title}</h3>
						<p className="-mt-2 text-center text-sm text-muted-foreground">{section.description}</p>
					</div>
				))}
			</div>
		</div>
	);
}
