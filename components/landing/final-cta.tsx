import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ROUTE_PATH_SIGN_IN } from "@/lib/constants";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

export default function FinalCTA({
	ctaText = "Start For Now",
	ctaUrl = ROUTE_PATH_SIGN_IN,
	title = "",
	description,
}: {
	ctaText?: string;
	ctaUrl?: string;
	title?: string;
	description?: string;
}) {
	return (
		<div className="container flex flex-col items-center gap-8 rounded-xl bg-muted px-6 py-16">
			<div className="text-center">
				<h2 className="text-pretty text-[32px] font-semibold">{title}</h2>
				{description && <p className="mx-auto mt-4 max-w-4xl text-pretty text-muted-foreground">{description}</p>}
			</div>

			<div className="flex flex-col items-center gap-3">
				<NoPrefetchLink
					href={ctaUrl}
					className={cn(
						buttonVariants({ size: "lg", variant: "default" }),
						"h-12 bg-teal-500 px-11 after:content-(--content) hover:bg-teal-600",
					)}
					style={{ "--content": `'${ctaText}'` } as React.CSSProperties}
				></NoPrefetchLink>
			</div>
		</div>
	);
}
