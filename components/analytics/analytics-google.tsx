import { GA_TOKEN } from "@/lib/constants";
import Script from "next/script";

export function AnalyticsGoolge() {
	return (
		<div className="container">
			<Script src={`https://www.googletagmanager.com/gtag/js?id=G-${GA_TOKEN}`} />
			<Script id="google-analytics">
				{`
					window.dataLayer = window.dataLayer || [];
					function gtag(){dataLayer.push(arguments);}
					gtag('js', new Date());
			
					gtag('config', 'G-${GA_TOKEN}');
				`}
			</Script>
		</div>
	);
}
